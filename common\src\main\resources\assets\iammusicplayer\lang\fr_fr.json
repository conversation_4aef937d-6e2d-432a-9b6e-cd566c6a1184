{"_comment.i": "<PERSON><PERSON>", "item.iammusicplayer.radio_antenna": "Antenne radio", "item.iammusicplayer.parabolic_antenna": "Antenne Parabolique", "item.iammusicplayer.cassette_tape": "Cassette", "item.iammusicplayer.cassette_tape_glass": "Cassette en verre", "item.iammusicplayer.cassette_tape.written": "Cassette écrite", "item.iammusicplayer.cassette_tape_glass.written": "Cassette en verre écrite", "item.iammusicplayer.manual": "Manuel <PERSON>", "_comment.bl": "Block", "block.iammusicplayer.music_manager": "Gestionnaire de musique", "block.iammusicplayer.cassette_deck": "Lecteur de cassettes", "block.iammusicplayer.boombox": "Radiocassette", "_comment.g": "GUI", "imp.monitor.music_manager.test": "Test", "imp.monitor.music_manager.play_list": "<PERSON><PERSON> de lecture", "imp.monitor.music_manager.add_play_list": "Ajouter une liste de lecture", "imp.monitor.music_manager.create_play_list": "<PERSON><PERSON><PERSON> une liste de lecture", "imp.monitor.music_manager.add_music": "Ajouter une musique", "imp.monitor.music_manager.search_music": "Recherche de musique", "imp.monitor.music_manager.upload_music": "Télécharger de la musique", "imp.monitor.music_manager.add_online_play_list": "Ajouter une liste de lecture en ligne", "imp.monitor.music_manager.import_youtube_play_list": "Importer une liste de lecture youtube", "imp.monitor.music_manager.detail_play_list": "<PERSON><PERSON><PERSON> de la liste de lecture", "imp.monitor.music_manager.edit_play_list": "Modifier la liste de lecture", "imp.monitor.music_manager.import_play_list_select": "Importer la liste de lecture selectionée", "imp.monitor.music_manager.detail_music": "Informations sur la musique", "imp.monitor.music_manager.edit_music": "Modifier la musique", "imp.monitor.music_manager.delete_play_list": "Supprimer la liste de lecture", "imp.monitor.music_manager.delete_music": "Supprimer la musique", "imp.monitor.music_manager.import_musics_select": "Importer la liste de lecture selectionée", "imp.monitor.music_manager.import_youtube_play_list_musics": "Importer une liste de lecture youtube", "imp.monitor.music_manager.authority": "Autorisation", "imp.monitor.boombox.off": "Off", "imp.monitor.boombox.playing": "<PERSON><PERSON>", "imp.monitor.boombox.radio": "Radio", "imp.button.power": "Alimentation", "imp.button.close": "<PERSON><PERSON><PERSON>", "imp.button.backScreen": "Revenir à l'écran précédent", "imp.button.sort": "<PERSON><PERSON>", "imp.button.addPlaylist": "Ajouter une liste de lecture", "imp.button.addMusic": "Ajouter une musique", "imp.button.createPlaylist": "<PERSON><PERSON><PERSON> une liste de lecture", "imp.button.addOnlinePlaylist": "Ajouter une liste de lecture en ligne", "imp.button.imageSet.file_open": "<PERSON><PERSON><PERSON><PERSON><PERSON> un fichier", "imp.button.imageSet.player_face": "Icône du visage du joueur", "imp.button.imageSet.delete": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.imageSet.url": "Spécifiez l'URL", "imp.button.back": "Retour", "imp.button.create": "<PERSON><PERSON><PERSON>", "imp.button.add": "Ajouter", "imp.button.save": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.addInvitePlayer": "A<PERSON>ter un joueur invité", "imp.button.playbackControl": "Contr<PERSON><PERSON> de <PERSON>", "imp.button.search": "Recherche", "imp.button.uploadMusic": "Télécharger la musique", "imp.button.openFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> le <PERSON>er", "imp.button.write": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.playback": "Lecture", "imp.button.writeStart": "Début de l'écriture", "imp.button.boombox.none": "Aucun", "imp.button.boombox.power": "Alimentation", "imp.button.boombox.radio": "Radio", "imp.button.boombox.start": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.boombox.pause": "Pause", "imp.button.boombox.stop": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.boombox.loop": "<PERSON><PERSON><PERSON>", "imp.button.boombox.volDown": "Réduction du volume", "imp.button.boombox.volUp": "Augmentation du volume", "imp.button.boombox.volMute": "Couper le volume", "imp.button.boombox.volMax": "Volume maximum", "imp.button.importYoutubePlayList": "Importer une liste de lecture youtube", "imp.button.edit": "Modifier", "imp.button.delete": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.exit": "Sortir", "imp.button.detailPlaylist": "Détail", "imp.button.import": "Importer", "imp.button.authority": "Autorisation", "imp.button.expulsion": "Expulsion", "imp.button.radioStreamStart": "Début de la diffusion radio", "imp.progressBar.playbackControl": "Barre de progression du contrôle de la lecture", "imp.radioButton.public": "Publique", "imp.radioButton.private": "Priv<PERSON>", "imp.radioButton.readonly": "Lecture seulement", "imp.radioButton.member": "Membre", "imp.radioButton.ban": "Bannir", "imp.radioButton.admin": "Administrateur", "imp.fixedList.myPlaylist": "<PERSON> <PERSON><PERSON> de lecture", "imp.fixedList.joinPlaylist": "Rejoindre une liste de lecture", "imp.fixedList.onlinePlayers": "Joueurs en ligne", "imp.fixedList.invitePlayers": "Inviter des joueurs", "imp.fixedList.musicLoaderTypes": "Types de chargeurs de musique", "imp.fixedList.searchMusic": "Rechercher des musiques", "imp.fixedList.musics": "Musiques", "imp.fixedList.memberPlayers": "<PERSON><PERSON>urs membres", "imp.fixedList.youtubePlayListMusics": "Musiques de la liste de lecture de Youtube", "imp.fixedList.authorityPlayers": "Joueurs autorisés", "imp.sortType.name": "Tri par nom", "imp.sortType.player": "Tri des noms de joueurs", "imp.sortType.create_date": "<PERSON><PERSON><PERSON> un tri par date", "imp.orderType.ascending": "Ascendant", "imp.orderType.descending": "Descendant", "imp.editBox.imageUrl": "URL de l'image", "imp.editBox.name": "Nom", "imp.editBox.invitePlayerByName": "Inviter un joueur par son nom", "imp.editBox.musicSourceName": "Nom de la source musicale", "imp.editBox.musicSearchName": "Nom de la recherche musicale", "imp.editBox.youtubePlaylistIdentifier": "Identificateur de liste de lecture Youtube", "imp.editBox.radioUrl": "Radio url", "imp.widget.volume": "Volume", "imp.widget.playBackControl": "Contr<PERSON><PERSON> de <PERSON>", "imp.widget.loopControl": "Contrôle de <PERSON> boucle", "imp.widget.playProgressControl": "Contrôle PlayProgress", "imp.widget.continuousControl": "Contrôle continu", "imp.text.playerCount": "%s de joueurs", "imp.text.musicCount": "%s Musiques", "imp.text.invitation": "Invitation", "imp.text.addPlaylistInfo": "%s listes de lecture de %s lecteurs totalisant %s musique", "imp.text.playlistInfo": "%s playlists %s musique", "imp.text.public": "Publique", "imp.text.image": "Image", "imp.text.noImage": "Pas d'image", "imp.text.dropInfo": "Glisser-déposer pour définir l'image", "imp.text.imageLoad.empty": "Veuillez entrer l'URL", "imp.text.imageLoad.error": "Erreur de chargement!: %s", "imp.text.imageLoad.notImageUrl": "Pas l'URL de l'image", "imp.text.imageLoad.loadingImage": "Chargement de l'image...", "imp.text.imageLoad.optimizationImage": "Optimisation de l'image...", "imp.text.imageLoad.notImage": "Pas une image", "imp.text.imageLoad.uploadImage": "Téléchargement de l'image...", "imp.text.imageLoad.tooManyImages": "Trop d'images", "imp.text.imageLoad.fileNotFound": "Image non trouvée", "imp.text.imageLoad.uploadFailure": "Le téléchargement a échoué, veuillez réessayer: %s", "imp.text.imageLoad.directory": "Annuaire", "imp.text.name": "Nom", "imp.text.notEntered": "%s n'a pas été saisi", "imp.text.publishingSettings": "Paramètres de publication", "imp.text.initialAuthority": "Autorisation initiale", "imp.text.invite": "Inviter", "imp.text.uninvited": "Sans invitation", "imp.text.invited": "Invi<PERSON>", "imp.text.member": "Membre", "imp.text.invitePlayerByMCIDOrUUID": "Inviter un joueur par mcid ou uuid", "imp.text.playbackLoading": "Chargement...", "imp.text.musicSource": "Source de la musique", "imp.text.musicChecking": "Vérification...", "imp.text.musicGuessing": "Devine...", "imp.text.searching": "Recherche...", "imp.text.loaderTypeInfo.auto": "<PERSON><PERSON> quel est le chargeur de la chaîne", "imp.text.enterText.default": "Identifiant", "imp.text.enterText.auto": "Chaîne identifiable", "imp.text.enterText.youtube": "ID de la vidéo ou URL Youtube", "imp.text.enterText.soundcloud": "URL de Sound Cloud", "imp.text.enterText.url": "URL de la musique", "imp.text.loadFailure": "Impossible de charger", "imp.text.loadFailure.auto": "Impossible à deviner", "imp.text.musicAuthor": "Auteur: %s", "imp.text.relayServer": "<PERSON><PERSON><PERSON> relais", "imp.text.relayServer.response": "Vitesse de réponse: %sms Vitesse de traitement: %sms", "imp.text.relayServer.error": "Échec de la connexion: %s", "imp.text.relayServer.connectingChecking": "Vérification de connexion...", "imp.text.relayServer.uploadInfo": "La taille maximale du fichier est de %s", "imp.text.relayServer.uploading": "Téléchargement du fichier...", "imp.text.relayServer.warning": "Veuillez ne pas télécharger de données importantes telles que des informations confidentielles!", "imp.text.relayServer.responsibility": "Nous ne sommes pas responsables des dommages causés par le téléchargement.", "imp.text.relayServer.how": "Téléchargement sur Discord via un serveur relais.", "imp.text.uploadDropInfo": "Possibilité de télécharger un fichier par glisser-déposer", "imp.text.fileUpload.tooManyFiles": "Trop de fichiers", "imp.text.fileUpload.fileNotFound": "Fichier non trouvé", "imp.text.fileUpload.error": "Erreur de téléchargement: %s", "imp.text.fileUpload.failure": "%s: %s", "imp.text.fileUpload.sizeOver": "<PERSON><PERSON> sur", "imp.text.fileUpload.directory": "Annuaire", "imp.text.fileUpload.noURL": "URL vide", "imp.text.unknownPlayer": "Inconnu", "imp.text.noAntenna": "L'antenne n'est pas réglée", "imp.text.noCassetteTape": "Veuillez mettre la cassette", "imp.text.writing": "Écriture...", "imp.text.noMusicCassetteTape": "Aucune musique n'a été écrite", "imp.text.musicLoading": "Chargement de la musique...", "imp.text.streamLoading": "Chargement de la musique en streaming...", "imp.text.streamPlaying": "Lecture de musique en continu...", "imp.text.playlistLoading": "Chargement de la liste de lecture...", "imp.text.enterStream": "Entrez l'url du flux radio ou du live youtube", "imp.text.authority.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imp.text.authority.admin": "Administrateur", "imp.text.authority.member": "Membre", "imp.text.authority.read_only": "Lecture seulement", "imp.text.authority.invitation": "Invitation", "imp.text.authority.ban": "Bannir", "imp.text.authority.none": "Aucun", "imp.text.importMusicCount": "%s Musiques", "imp.text.importing": "Importation...", "imp.text.importFailure": "L'importation a échoué", "imp.text.deleteWarning": "%s' sera perdu à jamais! (Un long moment!)", "imp.text.cantChangeAuthority": "Vous ne pouvez pas changer les autorisations", "imp.text.radioChecking": "Vérification...", "imp.text.continuous.none": "Aucun", "imp.text.continuous.order": "Commander", "imp.text.continuous.random": "<PERSON><PERSON>", "imp.text.manual": "<PERSON>", "imp.text.manual.coverInfo": "Très facile à comprendre!", "imp.fileChooser.files.all": "To<PERSON> les fichiers", "imp.loaderType.auto": "Auto", "imp.loaderType.upload": "Télécharger", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "Http URL", "imp.loaderType.soundcloud": "Sound Cloud", "imp.fileChooser.title.music": "<PERSON><PERSON><PERSON><PERSON><PERSON> un fichier musical", "imp.fileChooser.title.image": "O<PERSON><PERSON><PERSON>r un fichier image", "imp.ringer.have": "%s que %s a", "imp.ringer.drop": "Déposé %s", "soundCategory.iammusicplayer": "Musique IMP", "imp.button.config": "Configuration IMP", "_comment.co": "Configuration", "text.autoconfig.iammusicplayer.title": "Iam Music Player Renewed Configuration", "text.autoconfig.iammusicplayer.category.client": "Côté client", "text.autoconfig.iammusicplayer.option.errorLog": "Journal des erreurs", "text.autoconfig.iammusicplayer.option.volume": "Volume", "text.autoconfig.iammusicplayer.option.maxPlayCont": "Durée maximale de lecture de la musique", "text.autoconfig.iammusicplayer.option.spatial": "Spatiale", "text.autoconfig.iammusicplayer.option.relayServerURL": "<PERSON><PERSON> du serveur relais", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "url de la bibliothèque native de LavaPlayer", "text.autoconfig.iammusicplayer.category.server": "<PERSON><PERSON><PERSON>", "text.autoconfig.iammusicplayer.option.maxWaitTime": "Temps d'attente maximum", "text.autoconfig.iammusicplayer.option.retryTime": "Temps de réessai", "text.autoconfig.iammusicplayer.option.dropItemRing": "S'il faut jouer dans l'objet déposé", "text.autoconfig.iammusicplayer.category.integration": "Intégration", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "Intégration Patchouli", "_comment.cc": "Config Value", "subtitleType.off": "Off", "subtitleType.vanilla": "Vanilla", "subtitleType.overlay": "Overlay", "_comment.c": "Command", "commands.imp.ringer.info": "Il y a actuellement %s sonneries et %s pendant la lecture (%s)", "commands.imp.ringer.info.all": "Il y a actuellement %s sonneries et %s pendant la lecture.", "commands.imp.ringer.list": "Les sonneries existantes sont les suivantes dans %s", "commands.imp.ringer.list.all.notFound": "La sonnerie n'a pas été trouvée", "commands.imp.ringer.list.notFound": "La sonnerie n'a pas été trouvée dans %s", "commands.imp.ringer.list.all": "Les sonneries existantes sont les suivantes", "commands.imp.ringer.list.entry.playing": "%s, existe à %s, et joue à", "commands.imp.ringer.list.entry": "%s, existe à %s", "commands.imp.ringer.list.all.entry.playing": "%s, exists at %s (%s), and plays at", "commands.imp.ringer.list.all.entry": "%s, existe à %s (%s)", "_comment.e": "Entity", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Advancements", "advancements.iammusicplayer.root.title": "Iam Music Player Renewed", "advancements.iammusicplayer.root.description": "Le mod de lecteur de musique ikisugi", "advancements.iammusicplayer.add_music.title": "Registre musical", "advancements.iammusicplayer.add_music.description": "Ajouter de la musique aux listes de lecture à l'aide du gestionnaire de musique", "advancements.iammusicplayer.write_cassette_tape.title": "Écriture...", "advancements.iammusicplayer.write_cassette_tape.description": "É<PERSON>rire de la musique sur des cassettes à l'aide d'un lecteur de cassettes", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "J'écoute de la musique", "advancements.iammusicplayer.listen_to_radio.title": "Une musique en direct", "advancements.iammusicplayer.listen_to_radio.description": "Écoutez le flux radio", "advancements.iammusicplayer.listen_to_remote_music.title": "Écoutez directement", "advancements.iammusicplayer.listen_to_remote_music.description": "Jouer une listes de lecture à l'aide d'une antenne parabolique", "advancements.iammusicplayer.listen_to_kamesuta.title": "Kame Power!!!", "advancements.iammusicplayer.listen_to_kamesuta.description": "Visionner une vidéo d'un kamesuta utilisant une antenne kamesuta", "_comment.bo": "Book", "book.imp.landing_text": "Ce livre décrit comment utiliser IamMusicPlayer.$(br2)Veuillez noter que la traduction anglaise peut être incorrecte car le créateur de ce mod est japonais.$(br2)Si vous trouvez des bugs dans ce mod ou des conflits avec d'autres mods, veuillez nous le faire savoir dans le $(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub issues$(/l).", "_comment.cr": "CreativeTab", "itemGroup.iammusicplayer.iammusicplayer": "Iam Music Player Renewed"}