package dev.felnull.imp.client.gui.components;

import dev.felnull.imp.client.gui.IIMPSmartRender;
import dev.felnull.imp.client.gui.screen.monitor.music_manager.MusicManagerMonitor;
import dev.felnull.imp.music.resource.IIMPComparable;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Tooltip;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import org.modsauce.otyacraftenginerenewed.client.util.OERenderUtils;

import java.util.Comparator;
import java.util.List;

public abstract class SortButton extends IMPButton implements IIMPSmartRender {
    protected final boolean longed;

    private SortButton(int x, int y, int w, int tx, Component component, OnPress onPress, boolean longed, Screen screen) {
        super(x, y, w, 9, component, onPress);
        // super(x, y, w, 9, component, onPress, longed ? NO_TOOLTIP : (button, poseStack, px, py) -> screen.renderTooltip(poseStack, getText(button), px, py));
        // super(x, y, w, 9, tx, 30, 9, MusicManagerMonitor.WIDGETS_TEXTURE, 256, 256, onPress, longed ? NO_TOOLTIP : (button, poseStack, px, py) -> screen.renderTooltip(poseStack, getText(button), px, py), Component.translatable("imp.button.sort"));
        this.longed = longed;
    }

    @Override
    public void onPress() {
        cycle();
        setTooltip(Tooltip.create(getToolTipComponent()));
        super.onPress();
    }

    @Override
    public void renderWidget(GuiGraphics guiGraphics, int i, int j, float f) {
        drawSmartButtonBox(guiGraphics, getX(), getY(), width, height, isHoveredOrFocused());
      /*  if (this.isHoveredOrFocused())
            this.renderToolTip(poseStack, mx, my);*/
    }

    abstract public void cycle();

    abstract public Component getToolTipComponent();

    public static enum SortType {
        NAME("name", (o1, o2) -> {
            return o1.getCompareName().compareTo(o2.getCompareName());
        }), PLAYER("player", (o1, o2) -> {
            return o1.getComparePlayerName().compareTo(o2.getComparePlayerName());
        }), CREATE_DATE("create_date", (o1, o2) -> (int) (o1.getCompareDate() - o2.getCompareDate()));
        private final String name;
        private final Comparator<IIMPComparable> comparator;
        private final Comparator<IIMPComparable> rcomparator;
        private final Component componentName;

        SortType(String name, Comparator<IIMPComparable> comparator) {
            this.name = name;
            this.comparator = comparator;
            this.rcomparator = comparator.reversed();
            this.componentName = Component.translatable("imp.sortType." + name);
        }

        public String getName() {
            return name;
        }

        public Comparator<IIMPComparable> getComparator(OrderType orderType) {
            return orderType.isReverse() ? rcomparator : comparator;
        }

        public Component getComponentName() {
            return componentName;
        }
    }

    public static enum OrderType {
        ASCENDING("ascending", false), DESCENDING("descending", true);
        private final String name;
        private final boolean reverse;
        private final Component componentName;

        OrderType(String name, boolean reverse) {
            this.name = name;
            this.reverse = reverse;
            this.componentName = Component.translatable("imp.orderType." + name);
        }

        public boolean isReverse() {
            return reverse;
        }

        public String getName() {
            return name;
        }

        public Component getComponentName() {
            return componentName;
        }
    }

    public static class SortTypeButton extends SortButton {
        private SortType type = SortType.NAME;

        public SortTypeButton(int x, int y, OnPress onPress, boolean longed, Screen screen) {
            super(x, y, longed ? 97 : 9, longed ? 61 : 52, Component.translatable("imp.button.sort"), onPress, longed, screen);
            if (!longed)
                setTooltip(Tooltip.create(getToolTipComponent()));
        }

        @Override
        public void cycle() {
            type = SortType.values()[(type.ordinal() + 1) % SortType.values().length];
        }

        @Override
        public Component getToolTipComponent() {
            return type.getComponentName();
        }

        public SortType getSortType() {
            return type;
        }

        public <T extends IIMPComparable> List<T> sort(List<T> target, OrderTypeButton orderTypeButton) {
            return target.stream().sorted(getSortType().getComparator(orderTypeButton.getOrderType())).toList();
        }

        @Override
        public void renderWidget(GuiGraphics guiGraphics, int i, int j, float f) {
            super.renderWidget(guiGraphics, i, j, f);
            OERenderUtils.drawTexture(MusicManagerMonitor.WIDGETS_TEXTURE, guiGraphics.pose(), getX() + 1, getY() + 1, 73 + getSortType().ordinal() * 7, 0, 7, 7);
            if (longed) {
                drawSmartText(guiGraphics, getToolTipComponent(), getX() + 9, getY() + 1);
            }
        }
    }

    public static class OrderTypeButton extends SortButton {
        private OrderType type = OrderType.DESCENDING;

        public OrderTypeButton(int x, int y, OnPress onPress, boolean longed, Screen screen) {
            super(x, y, longed ? 88 : 9, longed ? 158 : 52, Component.translatable("imp.button.order"), onPress, longed, screen);
            if (!longed)
                setTooltip(Tooltip.create(getToolTipComponent()));
        }

        @Override
        public void cycle() {
            type = OrderType.values()[(type.ordinal() + 1) % OrderType.values().length];
        }

        @Override
        public Component getToolTipComponent() {
            return type.getComponentName();
        }

        public OrderType getOrderType() {
            return type;
        }

        @Override
        public void renderWidget(GuiGraphics guiGraphics, int i, int j, float f) {
            super.renderWidget(guiGraphics, i, j, f);
            OERenderUtils.drawTexture(MusicManagerMonitor.WIDGETS_TEXTURE, guiGraphics.pose(), getX() + 1, getY() + 1, 73 + getOrderType().ordinal() * 7, 7, 7, 7);
            if (longed) {
                drawSmartText(guiGraphics, getToolTipComponent(), getX() + 9, getY() + 1);
            }
        }
    }
}
