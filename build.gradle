plugins {
    id "architectury-plugin" version "3.4-SNAPSHOT"
    id "dev.architectury.loom" version "1.10-SNAPSHOT" apply false
    id 'org.jetbrains.changelog' version "2.0.0"
}

architectury {
    minecraft = rootProject.minecraft_version
}

changelog {
    repositoryUrl = rootProject.repository_url
    introduction = """
    Changelog to track updates for this mod.  
    Add your changes to Unreleased if you want to commit.  
    Please write according to [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
    """
    combinePreReleases = false
}

subprojects {
    apply plugin: "dev.architectury.loom"

    loom {
        silentMojangMappingsLicense()
    }

    dependencies {
        minecraft "com.mojang:minecraft:${rootProject.minecraft_version}"
        mappings loom.officialMojangMappings()
    }
}

allprojects {
    apply plugin: "java"
    apply plugin: "architectury-plugin"
    apply plugin: "maven-publish"

//    archivesName = rootProject.archives_base_name
    version = rootProject.mod_version
    //group = rootProject.maven_group

    repositories {
        maven {
            url = repsyUrl
            credentials {
                username = repsyUsername
                password = repsyPassword
            }
        }
        maven {
            url = "https://maven.felnull.dev"
        }
        maven {
            url = 'https://m2.dv8tion.net/releases'
        }
        maven {
            url = 'https://jitpack.io'
        }
        maven {
            url = 'https://maven.blamejared.com'
        }
        maven {
            url = "https://cursemaven.com"
        }
        maven {
            url = "https://maven.lavalink.dev/releases"
        }
    }

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
        options.release = 17
    }

    java {
        withSourcesJar()
    }
}