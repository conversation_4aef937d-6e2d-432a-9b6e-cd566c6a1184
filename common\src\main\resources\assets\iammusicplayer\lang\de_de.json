{"_comment.i": "Gegenstand", "item.iammusicplayer.radio_antenna": "Radio Antenne", "item.iammusicplayer.radio_antenna.desc": "Erforderlich zum Hören von Radio-Streams", "item.iammusicplayer.parabolic_antenna": "Parabolantenne", "item.iammusicplayer.parabolic_antenna.desc": "<PERSON><PERSON><PERSON><PERSON>lich, um Musik ohne Kassetten zu hören", "item.iammusicplayer.cassette_tape": "<PERSON><PERSON><PERSON>", "item.iammusicplayer.cassette_tape_glass": "Glass Kassette", "item.iammusicplayer.cassette_tape.written": "Written Ka<PERSON>tte", "item.iammusicplayer.cassette_tape_glass.written": "Beschriebenes Glass Kassette", "item.iammusicplayer.manual": "Iam Music Player Renewed <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.iammusicplayer.invalid_item.patchouli.desc": "<PERSON><PERSON><PERSON> muss <PERSON>t sein", "item.iammusicplayer.invalid_item.patchouli.config.desc": "Die Integration mit Patchouli muss in der Konfiguration aktiviert werden", "item.iammusicplayer.invalid_item.desc": "<PERSON><PERSON> Item ist ungültig!", "_comment.bl": "Block", "block.iammusicplayer.music_manager": "Musik Manager", "block.iammusicplayer.cassette_deck": "Kassettenspieler", "block.iammusicplayer.boombox": "Musikbox", "_comment.g": "GUI", "imp.monitor.music_manager.test": "Test", "imp.monitor.music_manager.play_list": "Wiedergabeliste", "imp.monitor.music_manager.add_play_list": "Füge Wiedergabeliste hinzu", "imp.monitor.music_manager.create_play_list": "<PERSON><PERSON><PERSON> eine Wiedergabeliste", "imp.monitor.music_manager.add_music": "Füge Musik hinzu", "imp.monitor.music_manager.search_music": "<PERSON><PERSON>", "imp.monitor.music_manager.upload_music": "Lade Musik hoch", "imp.monitor.music_manager.add_online_play_list": "Füge online Wiedergabeliste hinzu", "imp.monitor.music_manager.import_youtube_play_list": "Importiere Youtube Wiedergabelist", "imp.monitor.music_manager.detail_play_list": "Wiedergabelist Details", "imp.monitor.music_manager.edit_play_list": "Bearbeite Wiedergabelist", "imp.monitor.music_manager.import_play_list_select": "Auswahl der Import-Wiedergabeliste", "imp.monitor.music_manager.detail_music": "Musik Details", "imp.monitor.music_manager.edit_music": "Bearbei<PERSON> Musik", "imp.monitor.music_manager.delete_play_list": "Lösche Wiedergabeliste", "imp.monitor.music_manager.delete_music": "<PERSON>ö<PERSON>", "imp.monitor.music_manager.import_musics_select": "Musik importieren auswählen", "imp.monitor.music_manager.import_youtube_play_list_musics": "Musik aus YouTube Wiedergabelisten importieren", "imp.monitor.music_manager.authority": "Berechtigung", "imp.monitor.boombox.off": "Aus", "imp.monitor.boombox.playing": "<PERSON>pielt", "imp.monitor.boombox.radio": "Radio", "imp.button.power": "An/Aus", "imp.button.close": "Schließen", "imp.button.backScreen": "Zurück", "imp.button.sort": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.addPlaylist": "Füge Wiedergabeliste hinzu", "imp.button.addMusic": "Füge Musik hinzu", "imp.button.createPlaylist": "<PERSON><PERSON><PERSON>iedergabeliste", "imp.button.addOnlinePlaylist": "Füge online Wiedergabeliste hinzu", "imp.button.imageSet.file_open": "<PERSON><PERSON>", "imp.button.imageSet.player_face": "Spielergesichtssymbol", "imp.button.imageSet.delete": "Löschen", "imp.button.imageSet.url": "URL Angeben", "imp.button.back": "Zurück", "imp.button.create": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.add": "Hinzufügen", "imp.button.save": "Speichern", "imp.button.addInvitePlayer": "<PERSON><PERSON> ein", "imp.button.playbackControl": "Wiedergabesteuerung", "imp.button.search": "<PERSON><PERSON>", "imp.button.uploadMusic": "Lade Musik hoch", "imp.button.openFile": "<PERSON><PERSON><PERSON>", "imp.button.write": "Schreibe", "imp.button.playback": "Wiedergabe", "imp.button.writeStart": "Starte Schreiben", "imp.button.boombox.none": "<PERSON><PERSON><PERSON>", "imp.button.boombox.power": "<PERSON><PERSON>", "imp.button.boombox.radio": "Radio", "imp.button.boombox.start": "Start", "imp.button.boombox.pause": "Pause", "imp.button.boombox.stop": "Stop", "imp.button.boombox.loop": "<PERSON><PERSON><PERSON><PERSON>", "imp.button.boombox.volDown": "Lautstärke verringern", "imp.button.boombox.volUp": "Lautstärke erhöhen", "imp.button.boombox.volMute": "<PERSON><PERSON><PERSON> schalten", "imp.button.boombox.volMax": "Maximale Lautstärke", "imp.button.importYoutubePlayList": "YouTube-Wiedergabeliste importieren", "imp.button.edit": "<PERSON><PERSON><PERSON>", "imp.button.delete": "Löschen", "imp.button.exit": "Schließen", "imp.button.detailPlaylist": "Detail", "imp.button.import": "Importieren", "imp.button.authority": "Berechtigung", "imp.button.expulsion": "Ausweisung", "imp.button.radioStreamStart": "Starte Radiostream", "imp.progressBar.playbackControl": "Fortschrittsbalken für die Wiedergabesteuerung", "imp.radioButton.public": "<PERSON><PERSON><PERSON><PERSON>", "imp.radioButton.private": "Privat", "imp.radioButton.readonly": "<PERSON><PERSON>", "imp.radioButton.member": "<PERSON><PERSON><PERSON><PERSON>", "imp.radioButton.ban": "Ban", "imp.radioButton.admin": "Administrator", "imp.fixedList.myPlaylist": "<PERSON><PERSON> Wiedergabelist<PERSON>", "imp.fixedList.joinPlaylist": "Playlist beitreten", "imp.fixedList.onlinePlayers": "Online Spieler", "imp.fixedList.invitePlayers": "<PERSON><PERSON> ein", "imp.fixedList.musicLoaderTypes": "Music Lade-Typen", "imp.fixedList.searchMusic": "<PERSON><PERSON>", "imp.fixedList.musics": "Mu<PERSON>", "imp.fixedList.memberPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imp.fixedList.youtubePlayListMusics": "Youtube-Playlist-<PERSON><PERSON>", "imp.fixedList.authorityPlayers": "Autoritätsspieler", "imp.sortType.name": "Nach Name sortieren", "imp.sortType.player": "Nach Spielernamen sortieren", "imp.sortType.create_date": "Nach Erstelldatum sortieren", "imp.orderType.ascending": "Aufsteigen", "imp.orderType.descending": "Absteigen", "imp.editBox.imageUrl": "Bild URL", "imp.editBox.name": "Name", "imp.editBox.invitePlayerByName": "<PERSON>de S<PERSON>ler mit Namen ein", "imp.editBox.musicSourceName": "Musik Quellen Name", "imp.editBox.musicSearchName": "Name der Musiksuche", "imp.editBox.youtubePlaylistIdentifier": "Youtube-Playlist-<PERSON><PERSON><PERSON>", "imp.editBox.radioUrl": "Radio url", "imp.widget.volume": "Lautstärke", "imp.widget.playBackControl": "Wiedergabesteuerung", "imp.widget.loopControl": "Schleifensteuerung", "imp.widget.playProgressControl": "Wiedergabefortschrittskontrolle", "imp.widget.continuousControl": "Kontinuierliche Kontrolle", "imp.text.playerCount": "%s <PERSON><PERSON>ler", "imp.text.musicCount": "%s Songs", "imp.text.invitation": "Einladung", "imp.text.addPlaylistInfo": "%s Wiedergabelisten von %s S<PERSON>lern, insgesamt %s Musik", "imp.text.playlistInfo": "%s Wiedergabelisten %s Musik", "imp.text.public": "<PERSON><PERSON><PERSON><PERSON>", "imp.text.image": "Bild", "imp.text.noImage": "<PERSON><PERSON>", "imp.text.dropInfo": "<PERSON><PERSON><PERSON> und Ablegen, um das Bild festzulegen", "imp.text.imageLoad.empty": "<PERSON>te geben sie eine URL ein ", "imp.text.imageLoad.error": "Lade Fehler!: %s", "imp.text.imageLoad.notImageUrl": "Nicht die URL eines Bildes", "imp.text.imageLoad.loadingImage": "Lade Bild...", "imp.text.imageLoad.optimizationImage": "Optimiere Bild...", "imp.text.imageLoad.notImage": "Dies ist kein Bild", "imp.text.imageLoad.uploadImage": "Bild wird hochgeladen...", "imp.text.imageLoad.tooManyImages": "<PERSON><PERSON><PERSON>le Bilder", "imp.text.imageLoad.fileNotFound": "Bild nicht gefunden", "imp.text.imageLoad.uploadFailure": "Hochladen fehlgeschlagen, bitte erneut versuchen: %s", "imp.text.imageLoad.directory": "Ordener", "imp.text.name": "Name", "imp.text.notEntered": "%s wurde nicht angegeben", "imp.text.publishingSettings": "Veröffentlichungseinstellungen", "imp.text.initialAuthority": "Ursprüngliche Autorität", "imp.text.invite": "Einladen", "imp.text.uninvited": "Nicht Eingeladen", "imp.text.invited": "Eingeladen", "imp.text.member": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imp.text.invitePlayerByMCIDOrUUID": "<PERSON><PERSON> mit mcid oder uuid", "imp.text.playbackLoading": "Lade...", "imp.text.musicSource": "<PERSON><PERSON>", "imp.text.musicChecking": "Controliere...", "imp.text.musicGuessing": "Rate...", "imp.text.searching": "Suche...", "imp.text.loaderTypeInfo.auto": "<PERSON>n <PERSON>, welcher Loader die Zeichenfolge ist", "imp.text.enterText.default": "Identifiziere", "imp.text.enterText.auto": "Identifizierbare Zeichenfolge", "imp.text.enterText.youtube": "Video ID oder Youtube URL", "imp.text.enterText.soundcloud": "Sound Cloud URL", "imp.text.enterText.neteasecloudmusic": "Netease Cloud Musik Song ID", "imp.text.enterText.url": "Musik URL", "imp.text.loadFailure": "Konnte nicht geladen werden", "imp.text.loadFailure.auto": "Konnte nicht raten", "imp.text.musicAuthor": "Autor: %s", "imp.text.relayServer": "Relay-Server", "imp.text.relayServer.response": "Antwortzeit: %sms  Verarbeitungszeit: %sms", "imp.text.relayServer.error": "Verbindung fehlgeschlagen: %s", "imp.text.relayServer.connectingChecking": "Überprüfe Verbindung...", "imp.text.relayServer.uploadInfo": "Maximale Dateien Größe von %s", "imp.text.relayServer.uploading": "Lade Datei hoch...", "imp.text.relayServer.warning": "Bitte lade keine wichtigen Daten wie vertrauliche Informationen hoch!", "imp.text.relayServer.responsibility": "Wir sind nicht verantwortlich für Schäden, die durch das Hochladen entstehen.", "imp.text.relayServer.how": "<PERSON><PERSON>laden auf Discord über einen Relay-Server.", "imp.text.uploadDropInfo": "<PERSON>nn <PERSON> per Drag & Drop hochladen", "imp.text.fileUpload.tooManyFiles": "<PERSON><PERSON><PERSON><PERSON>", "imp.text.fileUpload.fileNotFound": "Datei nicht gefunden", "imp.text.fileUpload.error": "Hochlade Fehler!: %s", "imp.text.fileUpload.failure": "%s: %s", "imp.text.fileUpload.sizeOver": "Größe über", "imp.text.fileUpload.directory": "Verzeichnis", "imp.text.fileUpload.noURL": "Leere URL", "imp.text.unknownPlayer": "Unbekannt", "imp.text.noAntenna": "<PERSON>tenne ist nicht einge<PERSON>t", "imp.text.noCassetteTape": "Bitte lege eine Kassette ein", "imp.text.writing": "Schreibe...", "imp.text.noMusicCassetteTape": "<PERSON>s wurde keine Musik geschrieben", "imp.text.musicLoading": "<PERSON><PERSON>...", "imp.text.streamLoading": "Stream-Musik wird geladen...", "imp.text.streamPlaying": "Stream-Musik abspielen...", "imp.text.playlistLoading": "Wiedergabeliste wird geladen...", "imp.text.enterStream": "<PERSON><PERSON><PERSON> Sie den Radiostream oder die YouTube Live-URL ein", "imp.text.authority.owner": "<PERSON><PERSON><PERSON><PERSON>", "imp.text.authority.admin": "Administrator", "imp.text.authority.member": "<PERSON><PERSON><PERSON><PERSON>", "imp.text.authority.read_only": "<PERSON><PERSON> lesen", "imp.text.authority.invitation": "Einladung", "imp.text.authority.ban": "Ban", "imp.text.authority.none": "<PERSON><PERSON><PERSON>", "imp.text.importMusicCount": "%s <PERSON><PERSON>", "imp.text.importing": "Importieren...", "imp.text.importFailure": "Importieren Fehlgeschlagen", "imp.text.deleteWarning": "%s‘ wird für immer verloren sein! (Für lange Zeit!)", "imp.text.cantChangeAuthority": "Du kannst die Autorität nicht ändern", "imp.text.radioChecking": "Überprüfen...", "imp.text.continuous.none": "<PERSON><PERSON><PERSON>", "imp.text.continuous.order": "<PERSON><PERSON><PERSON>", "imp.text.continuous.random": "Źufällig", "imp.text.manual": "<PERSON><PERSON>", "imp.text.manual.coverInfo": "<PERSON>hr leicht verständlich!", "imp.fileChooser.files.all": "<PERSON>e <PERSON>in", "imp.loaderType.auto": "Automatisch", "imp.loaderType.upload": "Hochladen", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "Http URL", "imp.loaderType.soundcloud": "Sound Cloud", "imp.loaderType.neteasecloudmusic": "Netease Cloud Music", "imp.fileChooser.title.music": "Öffne Musik Datein", "imp.fileChooser.title.image": "Öffne Musik Datein", "imp.ringer.have": "%s, die %s hat", "imp.ringer.drop": "Fallengelassen %s", "soundCategory.iammusicplayer": "IMPR Musik", "imp.button.config": "IMPR Konfiguration", "_comment.co": "Konfiguration", "text.autoconfig.iammusicplayer.title": "Iam Music Player Renewed Konfiguration", "text.autoconfig.iammusicplayer.category.client": "Client Seite", "text.autoconfig.iammusicplayer.option.volume": "Lautstärke", "text.autoconfig.iammusicplayer.option.maxPlayCont": "Maximale Anzahl der Musikwiedergaben", "text.autoconfig.iammusicplayer.option.spatial": "<PERSON><PERSON><PERSON><PERSON>", "text.autoconfig.iammusicplayer.option.sampleRate": "Abtastrate", "text.autoconfig.iammusicplayer.option.useYoutubeDownloader": "Nutze YoutubeDownloader", "text.autoconfig.iammusicplayer.option.relayServerURL": "Relay-Server url", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "URL der nativen LavaPlayer-Bibliothek", "text.autoconfig.iammusicplayer.option.neteaseCloudMusicApiURL": "", "text.autoconfig.iammusicplayer.option.hideDisplaySprite": "Anzeige-Sprite ausblenden", "text.autoconfig.iammusicplayer.option.hideDecorativeAntenna": "Dekorative Antenne verstecken", "text.autoconfig.iammusicplayer.category.server": "Server Seite", "text.autoconfig.iammusicplayer.option.maxWaitTime": "Maximale Wartezeit", "text.autoconfig.iammusicplayer.option.retryTime": "Wiederholungszeit", "text.autoconfig.iammusicplayer.option.dropItemRing": "Ob der weggeworfen Gegenstand gespielt werden soll", "text.autoconfig.iammusicplayer.category.integration": "Intigration", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "Patchouli-Integration", "text.autoconfig.iammusicplayer.option.soundPhysicsRemasteredIntegration": "SoundPhysicsRemastered Integration", "text.autoconfig.iammusicplayer.category.debug": "Debugg<PERSON>", "text.autoconfig.iammusicplayer.option.showMusicLines": "Musik-Timeline anzeigen", "text.autoconfig.iammusicplayer.option.showSpeakerRange": "Lautsprecherbereich anzeigen", "_comment.cc": "Konfigurationswert", "subtitleType.off": "Aus", "subtitleType.vanilla": "Vanilla", "subtitleType.overlay": "Überlagerung", "_comment.c": "<PERSON><PERSON><PERSON>", "commands.imp.ringer.info": "Derzeit sind %s Klingelen vorhanden und %s läuft während der Wiedergabe (%s)", "commands.imp.ringer.info.all": "Derzeit sind %s Klingeltöne vorhanden und %s während der Wiedergabe", "commands.imp.ringer.list": "Die vorhandenen Klingelen sind wie folgt in %s", "commands.imp.ringer.list.all.notFound": "<PERSON><PERSON><PERSON><PERSON> wurden nicht gefunden", "commands.imp.ringer.list.notFound": "<PERSON> Klingelton wurde in %s nicht gefunden.", "commands.imp.ringer.list.all": "The existing ringer are as follows", "commands.imp.ringer.list.entry.playing": "%s, existiert um %s und spielt", "commands.imp.ringer.list.entry": "%s, existiert um %s", "commands.imp.ringer.list.all.entry.playing": "%s, existiert um %s (%s) und spielt", "commands.imp.ringer.list.all.entry": "%s, existiert um %s (%s)", "_comment.e": "Entity", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Fortschritte", "advancements.iammusicplayer.root.title": "Iam Music Player Renewed", "advancements.iammusicplayer.root.description": "Die ikisugi Musik Spieler Mod", "advancements.iammusicplayer.add_music.title": "Musik Register", "advancements.iammusicplayer.add_music.description": "<PERSON><PERSON><PERSON> Musik zu Playlists hinzu mit den Musik Manager", "advancements.iammusicplayer.write_cassette_tape.title": "Schreiben...", "advancements.iammusicplayer.write_cassette_tape.description": "Schreiben Sie Musik auf Kassetten mit einem Kassettendeck", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "<PERSON><PERSON> höre Musik", "advancements.iammusicplayer.listen_to_radio.title": "Live Musik", "advancements.iammusicplayer.listen_to_radio.description": "H<PERSON>re den Radiostream", "advancements.iammusicplayer.listen_to_remote_music.title": "Direkt anhören", "advancements.iammusicplayer.listen_to_remote_music.description": "Playlists direkt über eine Parabolantenne abspielen", "advancements.iammusicplayer.listen_to_kamesuta.title": "Kame Power!!!", "advancements.iammusicplayer.listen_to_kamesuta.description": "Spiele ein Video eines Kamesuta mit einer Kamesuta-Antenne ab", "_comment.bo": "<PERSON><PERSON>", "book.imp.landing_text": "Dieses Buch beschreibt wie man die IamMusicPlayerRenewed verwendet. Wenn du irgend welche Probleme in dieser Mod oder Probleme mit anderen Mods findest, bitte erstalle ein $(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub issues$(/l).", "_comment.cr": "CreativeTab", "itemGroup.iammusicplayer.iammusicplayer": "Iam Music Player Renewed", "_comment.other": "<PERSON><PERSON>", "modmenu.descriptionTranslation.iammusicplayer": "Füge einen Musikplayer hinzu, mit dem du Musik auf mehreren Geräten hören kannst.\nDer Ikisugi-Musikplayer-Mod...", "_comment.la": "Zuletzt"}