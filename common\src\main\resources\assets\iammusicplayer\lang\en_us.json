{"_comment.i": "<PERSON><PERSON>", "item.iammusicplayer.radio_antenna": "Radio Antenna", "item.iammusicplayer.radio_antenna.desc": "Required to listen to radio streams", "item.iammusicplayer.parabolic_antenna": "Parabolic Antenna", "item.iammusicplayer.parabolic_antenna.desc": "Required to listen to music without using cassette tapes", "item.iammusicplayer.cassette_tape": "Cassette <PERSON>", "item.iammusicplayer.cassette_tape_glass": "Glass Cassette Tape", "item.iammusicplayer.cassette_tape.written": "Written <PERSON><PERSON>", "item.iammusicplayer.cassette_tape_glass.written": "Written Glass Cassette Tape", "item.iammusicplayer.manual": "Iam Music Player Renewed Manual", "item.iammusicplayer.invalid_item.patchouli.desc": "Patchouli must be installed", "item.iammusicplayer.invalid_item.patchouli.config.desc": "Integration with Patchouli must be enabled in config", "item.iammusicplayer.invalid_item.desc": "This item is invalid!", "_comment.bl": "Block", "block.iammusicplayer.music_manager": "Music Manager", "block.iammusicplayer.cassette_deck": "Cassette Deck", "block.iammusicplayer.boombox": "Boombox", "_comment.g": "GUI", "imp.monitor.music_manager.test": "Test", "imp.monitor.music_manager.play_list": "Play list", "imp.monitor.music_manager.add_play_list": "Add play list", "imp.monitor.music_manager.create_play_list": "Create play list", "imp.monitor.music_manager.add_music": "Add music", "imp.monitor.music_manager.search_music": "Search music", "imp.monitor.music_manager.upload_music": "Upload music", "imp.monitor.music_manager.add_online_play_list": "Add play list by online", "imp.monitor.music_manager.import_youtube_play_list": "Import youtube play list", "imp.monitor.music_manager.detail_play_list": "Play list details", "imp.monitor.music_manager.edit_play_list": "Edit play list", "imp.monitor.music_manager.import_play_list_select": "Import play list select", "imp.monitor.music_manager.detail_music": "Music details", "imp.monitor.music_manager.edit_music": "Edit music", "imp.monitor.music_manager.delete_play_list": "Delete play list", "imp.monitor.music_manager.delete_music": "Delete music", "imp.monitor.music_manager.import_musics_select": "Import music select", "imp.monitor.music_manager.import_youtube_play_list_musics": "Import youtube play list musics", "imp.monitor.music_manager.authority": "Authority", "imp.monitor.boombox.off": "Off", "imp.monitor.boombox.playing": "Playing", "imp.monitor.boombox.radio": "Radio", "imp.button.power": "Power", "imp.button.close": "Close", "imp.button.backScreen": "Return to the previous screen", "imp.button.sort": "Sort", "imp.button.addPlaylist": "Add play list", "imp.button.addMusic": "Add music", "imp.button.createPlaylist": "Create play list", "imp.button.addOnlinePlaylist": "Add play list by online", "imp.button.imageSet.file_open": "Open file", "imp.button.imageSet.player_face": "Player Face Icon", "imp.button.imageSet.delete": "Delete", "imp.button.imageSet.url": "Specify the URL", "imp.button.back": "Back", "imp.button.create": "Create", "imp.button.add": "Add", "imp.button.save": "Save", "imp.button.addInvitePlayer": "Add invite player", "imp.button.playbackControl": "Playback control", "imp.button.search": "Search", "imp.button.uploadMusic": "Upload Music", "imp.button.openFile": "Open File", "imp.button.write": "Write", "imp.button.playback": "Playback", "imp.button.writeStart": "Write Start", "imp.button.boombox.none": "None", "imp.button.boombox.power": "Power", "imp.button.boombox.radio": "Radio", "imp.button.boombox.start": "Start", "imp.button.boombox.pause": "pause", "imp.button.boombox.stop": "stop", "imp.button.boombox.loop": "loop", "imp.button.boombox.volDown": "Volume Down", "imp.button.boombox.volUp": "Volume Up", "imp.button.boombox.volMute": "Volume Mute", "imp.button.boombox.volMax": "Volume Max", "imp.button.importYoutubePlayList": "Import youtube playlist", "imp.button.edit": "Edit", "imp.button.delete": "Delete", "imp.button.exit": "Exit", "imp.button.detailPlaylist": "Detail", "imp.button.import": "Import", "imp.button.authority": "Authority", "imp.button.expulsion": "Expulsion", "imp.button.radioStreamStart": "Radio stream start", "imp.progressBar.playbackControl": "Playback control progress bar", "imp.radioButton.public": "Public", "imp.radioButton.private": "Private", "imp.radioButton.readonly": "Read only", "imp.radioButton.member": "Member", "imp.radioButton.ban": "Ban", "imp.radioButton.admin": "Admin", "imp.fixedList.myPlaylist": "My play list", "imp.fixedList.joinPlaylist": "Join play list", "imp.fixedList.onlinePlayers": "Online players", "imp.fixedList.invitePlayers": "Invite players", "imp.fixedList.musicLoaderTypes": "Music Loader Types", "imp.fixedList.searchMusic": "Search Musics", "imp.fixedList.musics": "Musics", "imp.fixedList.memberPlayers": "Member players", "imp.fixedList.youtubePlayListMusics": "Youtube play list musics", "imp.fixedList.authorityPlayers": "Authority players", "imp.sortType.name": "Name sort", "imp.sortType.player": "Player name sort", "imp.sortType.create_date": "Create date sort", "imp.orderType.ascending": "Ascending", "imp.orderType.descending": "Descending", "imp.editBox.imageUrl": "Image URL", "imp.editBox.name": "Name", "imp.editBox.invitePlayerByName": "Invite player by name", "imp.editBox.musicSourceName": "Music source name", "imp.editBox.musicSearchName": "Music search name", "imp.editBox.youtubePlaylistIdentifier": "Youtube play list identifier", "imp.editBox.radioUrl": "Radio url", "imp.widget.volume": "Volume", "imp.widget.playBackControl": "Play Back Control", "imp.widget.loopControl": "Loop Control", "imp.widget.playProgressControl": "PlayProgress Control", "imp.widget.continuousControl": "Continuous Control", "imp.text.playerCount": "%s Players", "imp.text.musicCount": "%s Musics", "imp.text.invitation": "Invitation", "imp.text.addPlaylistInfo": "%s playlists of %s players total %s music", "imp.text.playlistInfo": "%s playlists %s music", "imp.text.public": "Public", "imp.text.image": "Image", "imp.text.noImage": "No Image", "imp.text.dropInfo": "Drag and drop to set image", "imp.text.imageLoad.empty": "Please enter the URL", "imp.text.imageLoad.error": "Load Error!: %s", "imp.text.imageLoad.notImageUrl": "Not the URL of the image", "imp.text.imageLoad.loadingImage": "Loading image...", "imp.text.imageLoad.optimizationImage": "Optimizing image...", "imp.text.imageLoad.notImage": "Not an image", "imp.text.imageLoad.uploadImage": "Uploading image...", "imp.text.imageLoad.tooManyImages": "Too many images", "imp.text.imageLoad.fileNotFound": "Image not found", "imp.text.imageLoad.uploadFailure": "Upload failed, please try again: %s", "imp.text.imageLoad.directory": "Directory", "imp.text.name": "Name", "imp.text.notEntered": "%s have not been entered", "imp.text.publishingSettings": "Publishing settings", "imp.text.initialAuthority": "Initial authority", "imp.text.invite": "Invite", "imp.text.uninvited": "Uninvited", "imp.text.invited": "Invited", "imp.text.member": "Member", "imp.text.invitePlayerByMCIDOrUUID": "Invite player by mcid or uuid", "imp.text.playbackLoading": "Loading...", "imp.text.musicSource": "Music Source", "imp.text.musicChecking": "Checking...", "imp.text.musicGuessing": "Guessing...", "imp.text.searching": "Searching...", "imp.text.loaderTypeInfo.auto": "Guess which loader the string is", "imp.text.enterText.default": "Identifier", "imp.text.enterText.auto": "Identifiable String", "imp.text.enterText.youtube": "Video ID or Youtube URL", "imp.text.enterText.soundcloud": "Sound Cloud URL", "imp.text.enterText.neteasecloudmusic": "Netease Cloud Music Song ID", "imp.text.enterText.url": "Music URL", "imp.text.loadFailure": "Could not load", "imp.text.loadFailure.auto": "Could not guess", "imp.text.musicAuthor": "Author: %s", "imp.text.relayServer": "Relay Server", "imp.text.relayServer.response": "Response Speed: %sms  Processing speed: %sms", "imp.text.relayServer.error": "Connection Failed: %s", "imp.text.relayServer.connectingChecking": "Connecting checking...", "imp.text.relayServer.uploadInfo": "Maximum file size of %s", "imp.text.relayServer.uploading": "Uploading file...", "imp.text.relayServer.warning": "Please do not upload important data such as confidential information!", "imp.text.relayServer.responsibility": "We are not responsible for any damage caused by uploading.", "imp.text.relayServer.how": "Uploading to Discord via a relay server.", "imp.text.uploadDropInfo": "Can upload file by drag and drop", "imp.text.fileUpload.tooManyFiles": "Too many files", "imp.text.fileUpload.fileNotFound": "File not found", "imp.text.fileUpload.error": "Upload Error!: %s", "imp.text.fileUpload.failure": "%s: %s", "imp.text.fileUpload.sizeOver": "<PERSON>ze over", "imp.text.fileUpload.directory": "Directory", "imp.text.fileUpload.noURL": "Empty URL", "imp.text.unknownPlayer": "Unknown", "imp.text.noAntenna": "<PERSON><PERSON><PERSON> is not set", "imp.text.noCassetteTape": "Please set the cassette tape", "imp.text.writing": "Writing...", "imp.text.noMusicCassetteTape": "No music has been written", "imp.text.musicLoading": "Loading music...", "imp.text.streamLoading": "Loading stream music...", "imp.text.streamPlaying": "Playing stream music...", "imp.text.playlistLoading": "Loading play list...", "imp.text.enterStream": "Enter radio stream or youtube live url", "imp.text.authority.owner": "Owner", "imp.text.authority.admin": "Admin", "imp.text.authority.member": "Member", "imp.text.authority.read_only": "Read only", "imp.text.authority.invitation": "Invitation", "imp.text.authority.ban": "Ban", "imp.text.authority.none": "None", "imp.text.importMusicCount": "%s Musics", "imp.text.importing": "Importing...", "imp.text.importFailure": "Import failed", "imp.text.deleteWarning": "%s' will be lost forever! (A long time!)", "imp.text.cantChangeAuthority": "You can't change authority", "imp.text.radioChecking": "Checking...", "imp.text.continuous.none": "None", "imp.text.continuous.order": "Order", "imp.text.continuous.random": "Random", "imp.text.manual": "Manual", "imp.text.manual.coverInfo": "Very easy to understand!", "imp.fileChooser.files.all": "All Files", "imp.loaderType.auto": "Auto", "imp.loaderType.upload": "Upload", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "Http URL", "imp.loaderType.soundcloud": "Sound Cloud", "imp.loaderType.neteasecloudmusic": "Netease Cloud Music", "imp.fileChooser.title.music": "Open music file", "imp.fileChooser.title.image": "Open image file", "imp.ringer.have": "%s that %s has", "imp.ringer.drop": "Dropped %s", "soundCategory.iammusicplayer": "IMP Music", "imp.button.config": "IMP Config", "_comment.co": "Config", "text.autoconfig.iammusicplayer.title": "Iam Music Player Renewed Config", "text.autoconfig.iammusicplayer.category.client": "Client Side", "text.autoconfig.iammusicplayer.option.volume": "Volume", "text.autoconfig.iammusicplayer.option.maxPlayCont": "Max music play cont", "text.autoconfig.iammusicplayer.option.spatial": "Spatial", "text.autoconfig.iammusicplayer.option.sampleRate": "Sample rate", "text.autoconfig.iammusicplayer.option.useYoutubeDownloader": "Use YoutubeDownloader", "text.autoconfig.iammusicplayer.option.relayServerURL": "Relay server url", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "LavaPlayer native library url", "text.autoconfig.iammusicplayer.option.neteaseCloudMusicApiURL": "Netease Cloud Music API url", "text.autoconfig.iammusicplayer.option.hideDisplaySprite": "Hide Display Sprite", "text.autoconfig.iammusicplayer.option.hideDecorativeAntenna": "Hide Decorative Antenna", "text.autoconfig.iammusicplayer.category.server": "Server Side", "text.autoconfig.iammusicplayer.option.maxWaitTime": "<PERSON> Wait Time", "text.autoconfig.iammusicplayer.option.retryTime": "Retry Time", "text.autoconfig.iammusicplayer.option.dropItemRing": "Whether to play in the dropped item", "text.autoconfig.iammusicplayer.category.integration": "Integration", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "Patchouli integration", "text.autoconfig.iammusicplayer.option.soundPhysicsRemasteredIntegration": "SoundPhysicsRemastered integration", "text.autoconfig.iammusicplayer.category.debug": "Debug", "text.autoconfig.iammusicplayer.option.showMusicLines": "Show music time line", "text.autoconfig.iammusicplayer.option.showSpeakerRange": "Show speaker range", "_comment.cc": "Config Value", "subtitleType.off": "Off", "subtitleType.vanilla": "Vanilla", "subtitleType.overlay": "Overlay", "_comment.c": "Command", "commands.imp.ringer.info": "There are currently %s ringers and %s during playback (%s)", "commands.imp.ringer.info.all": "There are currently %s ringers and %s during playback", "commands.imp.ringer.list": "The existing ringer are as follows in %s", "commands.imp.ringer.list.all.notFound": "Ringer was not found", "commands.imp.ringer.list.notFound": "Ringer was not found in %s", "commands.imp.ringer.list.all": "The existing ringer are as follows", "commands.imp.ringer.list.entry.playing": "%s, exists at %s, and is playing", "commands.imp.ringer.list.entry": "%s, exists at %s", "commands.imp.ringer.list.all.entry.playing": "%s, exists at %s (%s), and is playing", "commands.imp.ringer.list.all.entry": "%s, exists at %s (%s)", "_comment.e": "Entity", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Advancements", "advancements.iammusicplayer.root.title": "Iam Music Player Renewed", "advancements.iammusicplayer.root.description": "The ikisugi music player mod", "advancements.iammusicplayer.add_music.title": "Music register", "advancements.iammusicplayer.add_music.description": "Add music to playlists using Music Manager", "advancements.iammusicplayer.write_cassette_tape.title": "Writing...", "advancements.iammusicplayer.write_cassette_tape.description": "Write music on cassette tapes using a cassette deck", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "I listen to music", "advancements.iammusicplayer.listen_to_radio.title": "A Live music", "advancements.iammusicplayer.listen_to_radio.description": "Listen to the radio stream", "advancements.iammusicplayer.listen_to_remote_music.title": "Listen directly", "advancements.iammusicplayer.listen_to_remote_music.description": "Play playlists directly using a parabolic antenna", "advancements.iammusicplayer.listen_to_kamesuta.title": "Kame Power!!!", "advancements.iammusicplayer.listen_to_kamesuta.description": "Play a video of a kamesuta using a kamesuta antenna", "_comment.bo": "Book", "book.imp.landing_text": "This book describes how to use IamMusicPlayer.$(br2)Please note that the English translation may be incorrect because the creator of this mod is Japanese.$(br2)If you find any bugs in this mod or conflicts with other mods, please let us know in the $(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub issues$(/l).", "_comment.cr": "CreativeTab", "itemGroup.iammusicplayer.iammusicplayer": "Iam Music Player Renewed", "_comment.other": "Other", "modmenu.descriptionTranslation.iammusicplayer": "Add a music player that you can listen to with multiple players.\nThe ikisugi music player mod...", "_comment.la": "Last"}