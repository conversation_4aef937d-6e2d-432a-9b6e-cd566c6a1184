modLoader = "javafml"
loaderVersion = "48.1.0"
issueTrackerURL = "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/issues"
license = "GNU LGPLv3"

[[mods]]
modId = "iammusicplayer"
version = "${version}"
displayName = "Iam Music Player Renewed"
displayURL = "https://mod-sauce.github.io/impr.html"
updateJSONURL = ""
authors = "Us3r0, Shadowbee27"
credits = "Mod-Sauce & contributers"
description = '''
Add a boombox that can play music.
The ikisugi music player mod...
'''
logoFile = "logo.png"

[[dependencies.iammusicplayer]]
modId = "forge"
mandatory = true
versionRange = "48.1.0"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "minecraft"
mandatory = true
versionRange = "1.20.2"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "architectury"
mandatory = true
versionRange = "10.1.20"
ordering = "AFTER"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "cloth_config"
mandatory = true
versionRange = "12.0.137"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "otyacraftengine"
mandatory = true
versionRange = "3.7.0-1.20.2-alpha.1"
ordering = "NONE"
side = "BOTH"
