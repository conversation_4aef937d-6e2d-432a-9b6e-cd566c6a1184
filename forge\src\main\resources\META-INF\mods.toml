modLoader = "javafml"
loaderVersion = "[43,)"
issueTrackerURL = "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/issues"
license = "GNU LGPLv3"

[[mods]]
modId = "iammusicplayer"
version = "${version}"
displayName = "Iam Music Player Renewede"
displayURL = "https://www.curseforge.com/minecraft/mc-mods/iammusicplayer-renewed"
updateJSONURL = "https://raw.githubusercontent.com/TeamFelnull/IamMusicPlayer/master/version_check.json"
authors = "Mooo0042, Shadowbee27"
credits = "Mod-Sauce Dev's"
description = '''
Add a boombox that can play music.
The ikisugi music player mod...
'''
logoFile = "logo.png"

[modproperties.iammusicplayer]
catalogueImageIcon = "icon.png"

[[dependencies.iammusicplayer]]
modId = "forge"
mandatory = true
versionRange = "[43,)"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "minecraft"
mandatory = true
versionRange = "[1.19.2,)"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "architectury"
mandatory = true
versionRange = "[6.2.43,)"
ordering = "AFTER"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "cloth_config"
mandatory = true
versionRange = "[8.0.75,)"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "otyacraftengine"
mandatory = true
versionRange = "[3.2.0,)"
ordering = "NONE"
side = "BOTH"
