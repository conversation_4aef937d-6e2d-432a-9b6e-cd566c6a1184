modLoader = "javafml"
loaderVersion = "47.4.0"
issueTrackerURL = "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/issues"
license = "GNU LGPLv3"

[[mods]]
modId = "iammusicplayer"
version = "${version}"
displayName = "Iam Music Player Renewed"
displayURL = "https://mod-sauce.github.io/impr.html"
updateJSONURL = ""
authors = "Us3r0, Shadowbee27"
credits = "Mod-Sauce & contributers"
description = '''
Add a boombox that can play music.
The ikisugi music player mod...
'''
logoFile = "logo.png"

[[dependencies.iammusicplayer]]
modId = "forge"
mandatory = true
versionRange = "47.4.0"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "minecraft"
mandatory = true
versionRange = "1.20.1"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "architectury"
mandatory = true
versionRange = "9.2.14"
ordering = "AFTER"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "cloth_config"
mandatory = true
versionRange = "11.1.136"
ordering = "NONE"
side = "BOTH"

[[dependencies.iammusicplayer]]
modId = "otyacraftengine"
mandatory = true
versionRange = "3.7.0-1.20.1"
ordering = "NONE"
side = "BOTH"
