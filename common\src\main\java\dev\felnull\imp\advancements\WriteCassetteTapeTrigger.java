package dev.felnull.imp.advancements;

import com.google.gson.JsonObject;
import dev.felnull.imp.IamMusicPlayer;
import net.minecraft.advancements.critereon.*;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.item.ItemStack;

import java.util.Optional;

public class WriteCassetteTapeTrigger extends SimpleCriterionTrigger<WriteCassetteTapeTrigger.TriggerInstance> {
    private static final ResourceLocation ID = new ResourceLocation(IamMusicPlayer.MODID, "write_cassette_tape");

    /*@Override
    protected TriggerInstance createInstance(JsonObject jsonObject, EntityPredicate.Composite composite, DeserializationContext deserializationContext) {
        ItemPredicate itemPredicate = ItemPredicate.fromJson(jsonObject.get("item"));
        return new TriggerInstance(composite, itemPredicate);
    }*/
    protected TriggerInstance createInstance(JsonObject jsonObject, Optional<ContextAwarePredicate> contextAwarePredicate, DeserializationContext deserializationContext) {
        Optional<ItemPredicate> itemPredicate = ItemPredicate.fromJson(jsonObject.get("item"));
        return new TriggerInstance(contextAwarePredicate, itemPredicate.orElse(null));
    }

    public void trigger(ServerPlayer serverPlayer, ItemStack itemStack) {
        this.trigger(serverPlayer, (triggerInstance) -> triggerInstance.matches(itemStack));
    }

    public ResourceLocation getId() {
        return ID;
    }


    public static class TriggerInstance extends AbstractCriterionTriggerInstance {
        private final ItemPredicate item;

        public TriggerInstance(Optional<ContextAwarePredicate> contextAwarePredicate, ItemPredicate itemPredicat) {
            super(contextAwarePredicate);
            this.item = itemPredicat;
        }


        public boolean matches(ItemStack itemStack) {
            return item.matches(itemStack);
        }

        @Override
        public JsonObject serializeToJson() {
            JsonObject jsonObject = super.serializeToJson();
            if (this.item != null)
                jsonObject.add("item", this.item.serializeToJson());
            return jsonObject;
        }

        public static TriggerInstance writeCassetteTape() {
            return new TriggerInstance(Optional.empty(), null);
        }
    }
}
