{"_comment.i": "物品", "item.iammusicplayer.radio_antenna": "广播天线", "item.iammusicplayer.parabolic_antenna": "卫星天线", "item.iammusicplayer.cassette_tape": "磁带", "item.iammusicplayer.cassette_tape_glass": "透明磁带", "item.iammusicplayer.cassette_tape.written": "已写磁带", "item.iammusicplayer.cassette_tape_glass.written": "已写透明磁带", "item.iammusicplayer.manual": "IMP手册", "_comment.bl": "方块", "block.iammusicplayer.music_manager": "音乐管理电脑", "block.iammusicplayer.cassette_deck": "录音座", "block.iammusicplayer.boombox": "手提音响", "_comment.g": "界面", "imp.monitor.music_manager.test": "测试", "imp.monitor.music_manager.play_list": "播放列表", "imp.monitor.music_manager.add_play_list": "添加播放列表", "imp.monitor.music_manager.create_play_list": "新建播放列表", "imp.monitor.music_manager.add_music": "添加音乐", "imp.monitor.music_manager.search_music": "搜索音乐", "imp.monitor.music_manager.upload_music": "上传音乐", "imp.monitor.music_manager.add_online_play_list": "添加在线播放列表", "imp.monitor.music_manager.import_youtube_play_list": "导入youtube播放列表", "imp.monitor.music_manager.detail_play_list": "播放列表信息", "imp.monitor.music_manager.edit_play_list": "编辑播放列表", "imp.monitor.music_manager.import_play_list_select": "导入选择的播放列表", "imp.monitor.music_manager.detail_music": "音乐信息", "imp.monitor.music_manager.edit_music": "编辑音乐", "imp.monitor.music_manager.delete_play_list": "删除播放列表", "imp.monitor.music_manager.delete_music": "删除音乐", "imp.monitor.music_manager.import_musics_select": "导入选择的音乐", "imp.monitor.music_manager.import_youtube_play_list_musics": "导入youtube播放列表音乐", "imp.monitor.music_manager.authority": "权限", "imp.monitor.boombox.off": "关闭", "imp.monitor.boombox.playing": "播放", "imp.monitor.boombox.radio": "广播", "imp.button.power": "电源", "imp.button.close": "关上", "imp.button.backScreen": "返回上一界面", "imp.button.sort": "排序", "imp.button.addPlaylist": "添加播放列表", "imp.button.addMusic": "添加音乐", "imp.button.createPlaylist": "新建播放列表", "imp.button.addOnlinePlaylist": "添加在线播放列表", "imp.button.imageSet.file_open": "预览文件", "imp.button.imageSet.player_face": "玩家脸部图标", "imp.button.imageSet.delete": "删除", "imp.button.imageSet.url": "指定直链", "imp.button.back": "返回", "imp.button.create": "创建", "imp.button.add": "添加", "imp.button.save": "保存", "imp.button.addInvitePlayer": "邀请玩家", "imp.button.playbackControl": "播放控制", "imp.button.search": "搜索", "imp.button.uploadMusic": "上传音乐", "imp.button.openFile": "预览文件", "imp.button.write": "写入", "imp.button.playback": "播放", "imp.button.writeStart": "开始写入", "imp.button.boombox.none": "无", "imp.button.boombox.power": "电源", "imp.button.boombox.radio": "广播", "imp.button.boombox.start": "播放", "imp.button.boombox.pause": "暂停", "imp.button.boombox.stop": "停止", "imp.button.boombox.loop": "循环", "imp.button.boombox.volDown": "减小音量", "imp.button.boombox.volUp": "增大音量", "imp.button.boombox.volMute": "静音", "imp.button.boombox.volMax": "最大音量", "imp.button.importYoutubePlayList": "导入youtube播放列表", "imp.button.edit": "编辑", "imp.button.delete": "删除", "imp.button.exit": "离开", "imp.button.detailPlaylist": "信息", "imp.button.import": "导入", "imp.button.authority": "权限", "imp.button.expulsion": "移除", "imp.button.radioStreamStart": "启动广播流媒体", "imp.progressBar.playbackControl": "播放控制进度条", "imp.radioButton.public": "公开", "imp.radioButton.private": "私人", "imp.radioButton.readonly": "只读", "imp.radioButton.member": "成员", "imp.radioButton.ban": "屏蔽", "imp.radioButton.admin": "管理员", "imp.fixedList.myPlaylist": "我的播放列表", "imp.fixedList.joinPlaylist": "加入播放列表", "imp.fixedList.onlinePlayers": "在线玩家", "imp.fixedList.invitePlayers": "邀请玩家", "imp.fixedList.musicLoaderTypes": "音乐加载类型", "imp.fixedList.searchMusic": "搜索音乐", "imp.fixedList.musics": "音乐", "imp.fixedList.memberPlayers": "玩家成员", "imp.fixedList.youtubePlayListMusics": "Youtube播放列表音乐", "imp.fixedList.authorityPlayers": "玩家权限", "imp.sortType.name": "名称排序", "imp.sortType.player": "玩家名称排序", "imp.sortType.create_date": "创建日期排序", "imp.orderType.ascending": "升序", "imp.orderType.descending": "降序", "imp.editBox.imageUrl": "图片直链", "imp.editBox.name": "名称", "imp.editBox.invitePlayerByName": "按名称邀请玩家", "imp.editBox.musicSourceName": "音乐源名称", "imp.editBox.musicSearchName": "音乐搜索名称", "imp.editBox.youtubePlaylistIdentifier": "Youtube播放列表ID", "imp.editBox.radioUrl": "广播流媒体音乐", "imp.widget.volume": "音量", "imp.widget.playBackControl": "播放控制", "imp.widget.loopControl": "循环控制", "imp.widget.playProgressControl": "播放进度控制", "imp.widget.continuousControl": "连续控制", "imp.text.playerCount": "%s玩家", "imp.text.musicCount": "%s音乐", "imp.text.invitation": "邀请", "imp.text.addPlaylistInfo": "%s个播放列表来自%s名玩家总计%s首音乐", "imp.text.playlistInfo": "%s个播放列表%s首音乐", "imp.text.public": "公开", "imp.text.image": "图标", "imp.text.noImage": "没有图标", "imp.text.dropInfo": "拖放以设置图标", "imp.text.imageLoad.empty": "请输入直链", "imp.text.imageLoad.error": "加载错误！：%s", "imp.text.imageLoad.notImageUrl": "不是图像直链", "imp.text.imageLoad.loadingImage": "正在加载图像……", "imp.text.imageLoad.optimizationImage": "正在优化图像……", "imp.text.imageLoad.notImage": "不是图像", "imp.text.imageLoad.uploadImage": "正在上传图像……", "imp.text.imageLoad.tooManyImages": "图像太大", "imp.text.imageLoad.fileNotFound": "未找到图像", "imp.text.imageLoad.uploadFailure": "上传失败，请重试：%s", "imp.text.imageLoad.directory": "目录", "imp.text.name": "名称", "imp.text.notEntered": "%s尚未输入", "imp.text.publishingSettings": "显示设置", "imp.text.initialAuthority": "初始权限", "imp.text.invite": "邀请", "imp.text.uninvited": "未被邀请的", "imp.text.invited": "已被邀请", "imp.text.member": "成员", "imp.text.invitePlayerByMCIDOrUUID": "通过名称或UUID邀请玩家", "imp.text.playbackLoading": "加载中……", "imp.text.musicSource": "音乐源", "imp.text.musicChecking": "检验中……", "imp.text.musicGuessing": " 检测中……", "imp.text.searching": "搜索中……", "imp.text.loaderTypeInfo.auto": "检测链接是什么加载类型", "imp.text.enterText.default": "ID", "imp.text.enterText.auto": "可识别链接", "imp.text.enterText.youtube": "视频ID或Youtube链接", "imp.text.enterText.soundcloud": "Sound Cloud链接", "imp.text.enterText.url": "音乐直链", "imp.text.loadFailure": "无法加载", "imp.text.loadFailure.auto": "无法检测", "imp.text.musicAuthor": "作者：%s", "imp.text.relayServer": "中继服务器", "imp.text.relayServer.response": "响应速度：%sms  处理速度：%sms", "imp.text.relayServer.error": "连接失败：%s", "imp.text.relayServer.connectingChecking": "正在检查连接……", "imp.text.relayServer.uploadInfo": "%s的最大文件大小", "imp.text.relayServer.uploading": "正在上载文件……", "imp.text.relayServer.warning": "请勿上传机密信息等重要数据！", "imp.text.relayServer.responsibility": "我们不对上传造成的任何数据损坏负责。", "imp.text.relayServer.how": "通过中继服务器上传到Discord", "imp.text.uploadDropInfo": "可以通过拖放上传文件", "imp.text.fileUpload.tooManyFiles": "文件太大", "imp.text.fileUpload.fileNotFound": "未找到文件", "imp.text.fileUpload.error": "上传错误：%s", "imp.text.fileUpload.failure": "%s：%s", "imp.text.fileUpload.sizeOver": "尺寸过大", "imp.text.fileUpload.directory": "目录", "imp.text.fileUpload.noURL": "空链接", "imp.text.unknownPlayer": "未知", "imp.text.noAntenna": "未安装天线", "imp.text.noCassetteTape": "请放置磁带", "imp.text.writing": "写入中……", "imp.text.noMusicCassetteTape": "没有被写入音乐", "imp.text.musicLoading": "加载音乐中……", "imp.text.streamLoading": "加载流媒体音乐中……", "imp.text.streamPlaying": "播放流媒体音乐中……", "imp.text.playlistLoading": "加载播放列表中……", "imp.text.enterStream": "输入广播流媒体或者youtube live链接", "imp.text.authority.owner": "所有者", "imp.text.authority.admin": "管理员", "imp.text.authority.member": "成员", "imp.text.authority.read_only": "只读", "imp.text.authority.invitation": "邀请", "imp.text.authority.ban": "屏蔽", "imp.text.authority.none": "无", "imp.text.importMusicCount": "%s音乐", "imp.text.importing": "导入中……", "imp.text.importFailure": "导入失败", "imp.text.deleteWarning": "%s将会永远消失！（真的很久！）", "imp.text.cantChangeAuthority": "你不能改变权限", "imp.text.radioChecking": "检查中……", "imp.text.continuous.none": "无", "imp.text.continuous.order": "顺序", "imp.text.continuous.random": "随机", "imp.text.manual": "手册", "imp.text.manual.coverInfo": "非常容易理解！", "imp.fileChooser.files.all": "所有文件", "imp.loaderType.auto": "自动", "imp.loaderType.upload": "上传", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "直链", "imp.loaderType.soundcloud": "Sound Cloud", "imp.fileChooser.title.music": "打开音乐文件", "imp.fileChooser.title.image": "打开图标文件", "imp.ringer.have": "%s表示%s具有", "imp.ringer.drop": "丢弃%s", "soundCategory.iammusicplayer": "IMP音乐", "imp.button.config": "IMP设置", "_comment.co": "Config", "text.autoconfig.iammusicplayer.title": "Iam Music Player设置", "text.autoconfig.iammusicplayer.category.client": "客户端", "text.autoconfig.iammusicplayer.option.errorLog": "错误日志", "text.autoconfig.iammusicplayer.option.volume": "音量", "text.autoconfig.iammusicplayer.option.maxPlayCont": "音乐最大音量播放控制", "text.autoconfig.iammusicplayer.option.spatial": "空间音效", "text.autoconfig.iammusicplayer.option.relayServerURL": "中继服务器地址", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "LavaPlayer本地库地址", "text.autoconfig.iammusicplayer.category.server": "服务端", "text.autoconfig.iammusicplayer.option.maxWaitTime": "最长等待时间", "text.autoconfig.iammusicplayer.option.retryTime": "重试时间", "text.autoconfig.iammusicplayer.option.dropItemRing": "是否在丢弃的物品中播放", "text.autoconfig.iammusicplayer.category.integration": "兼容", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "帕秋莉手册兼容", "_comment.cc": "数值配置", "subtitleType.off": "关闭", "subtitleType.vanilla": "原版", "subtitleType.overlay": "覆盖", "_comment.c": "控制", "commands.imp.ringer.info": "当前有%s个Ringer有%s个正在播放（%s）", "commands.imp.ringer.info.all": "当前有%s个Ringer有%s个正在播放", "commands.imp.ringer.list": "现有在%s中的Ringer如下", "commands.imp.ringer.list.all.notFound": "未找到Ringer", "commands.imp.ringer.list.notFound": "在%s中找不到Ringer", "commands.imp.ringer.list.all": "现有Ringer如下", "commands.imp.ringer.list.entry.playing": "%s，存在于%s，而且正在播放", "commands.imp.ringer.list.entry": "%s，存在于%s", "commands.imp.ringer.list.all.entry.playing": "%s，存在于%s（%s），而且正在播放", "commands.imp.ringer.list.all.entry": "%s，存在于%s（%s）", "_comment.e": "实体", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Advancements", "advancements.iammusicplayer.root.title": "<PERSON>am Music Player", "advancements.iammusicplayer.root.description": "一个ikisugi music player mod", "advancements.iammusicplayer.add_music.title": "音乐记录器", "advancements.iammusicplayer.add_music.description": "使用音乐管理器将音乐添加到播放列表", "advancements.iammusicplayer.write_cassette_tape.title": "写入中……", "advancements.iammusicplayer.write_cassette_tape.description": "使用录音座在往磁带上写入音乐", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "我在听音乐", "advancements.iammusicplayer.listen_to_radio.title": "一首现场音乐", "advancements.iammusicplayer.listen_to_radio.description": "收听广播流媒体", "advancements.iammusicplayer.listen_to_remote_music.title": "直接播放", "advancements.iammusicplayer.listen_to_remote_music.description": "使用卫星天线直接播放播放列表中的音乐", "advancements.iammusicplayer.listen_to_kamesuta.title": "KAME之力！！！", "advancements.iammusicplayer.listen_to_kamesuta.description": "使用kamesuta天线播放kamesuta的视频", "_comment.bo": "书", "book.imp.landing_text": "本书介绍了如何使用IamMusicPlayer。$(br2)请注意，中文翻译可能不正确，因为此mod的作者是日本人，而中文根据英文翻译。$(br2)如果您发现此mod中存在错误或与其他mod冲突，请在$(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub中反馈$(/l).", "_comment.cr": "创造标签", "itemGroup.iammusicplayer.iammusicplayer": "<PERSON>am Music Player"}