{"schemaVersion": 1, "id": "iammusicplayer", "version": "${version}", "name": "Iam Music Player Renewed", "description": "Add a music player that you can listen to with multiple players.\nThe ikisugi music player mod...", "authors": ["Mooo0042", "Shadowbee27"], "contributors": ["<PERSON>d<PERSON><PERSON><PERSON>"], "contact": {"email": "<EMAIL>", "homepage": "https://www.curseforge.com/minecraft/mc-mods/iammusicplayer", "sources": "https://github.com/TeamFelnull/IamMusicPlayer", "issues": "https://github.com/TeamFelnull/IamMusicPlayer/issues"}, "license": "GNU LGPLv3", "icon": "assets/iammusicplayer/icon.png", "environment": "*", "entrypoints": {"main": ["dev.felnull.imp.fabric.IamMusicPlayerFabric"], "client": ["dev.felnull.imp.fabric.client.IamMusicPlayerClientFabric"], "fabric-datagen": ["dev.felnull.imp.fabric.data.IamMusicPlayerDataGeneratorFabric"], "otyacraftengine_client": ["dev.felnull.imp.client.entrypoint.IMPOEClientEntryPoint"]}, "mixins": ["iammusicplayer-common.mixins.json"], "depends": {"fabric": "*", "minecraft": "=1.19.2", "architectury": ">=6.2.43", "cloth-config2": ">=8.0.75", "otyacraftengine": ">=3.2.0"}}