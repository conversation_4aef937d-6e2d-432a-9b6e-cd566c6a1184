{"schemaVersion": 1, "id": "iammusicplayer", "version": "${version}", "name": "<PERSON>am Music Player", "description": "Add a music player that you can listen to with multiple players.\nThe ikisugi music player mod...", "authors": ["Shadowbee27", "Us3r0"], "contributors": ["Mod <PERSON> & contributors"], "contact": {"email": "<EMAIL>", "homepage": "https://mod-sauce.github.io/impr.html", "sources": "https://github.com/Mod-Sauce/IamMusicPlayer_FIX", "issues": "https://github.com/Mod-Sauce/IamMusicPlayer_FIX/issues"}, "license": "GNU LGPLv3", "icon": "assets/iammusicplayer/icon.png", "environment": "*", "entrypoints": {"main": ["dev.felnull.imp.fabric.IamMusicPlayerFabric"], "client": ["dev.felnull.imp.fabric.client.IamMusicPlayerClientFabric"], "fabric-datagen": ["dev.felnull.imp.fabric.data.IamMusicPlayerDataGeneratorFabric"], "otyacraftengine_client": ["dev.felnull.imp.client.entrypoint.IMPOEClientEntryPoint"]}, "mixins": ["iammusicplayer-common.mixins.json"], "depends": {"fabric": "*", "minecraft": "=1.20.1", "architectury": ">=9.2.14", "cloth-config2": ">=11.1.136", "otyacraftengine": ">=3.7.0-1.20.1"}}