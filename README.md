# IamMusicPlayer (IMP)

The Ikisugi Music Player MOD (Minecraft Forge/Fabric MOD)



# Contributor

``
fabric/src/main/generated
``  
``
forge/src/generated
``  
Do not edit these generated directories directly.  
If you want to change the contents, please change the IamMusicPlayerDataGenerator or change the resources folder.

## Transration

If a kind person translates it, please open an Pull requests!

[Templates used for translation](https://github.com/TeamFelnull/IamMusicPlayer/tree/master/common/src/main/resources/assets/iammusicplayer/lang)  
[Template used for translating patchouli](https://github.com/TeamFelnull/IamMusicPlayer/tree/master/resources/data/iammusicplayer/patchouli_books/manual)

# Using libraries

The following libraries are used for this mod,  
but they are included in the mod's jar file and do not need to be installed separately.

[LavaPlayer](https://github.com/sedmelluq/lavaplayer)  
[LavaPlayer (fork)](https://github.com/walkyst/lavaplayer-fork)   
[LavaPLayerNatives (macOS M1)](https://github.com/aikaterna/lavaplayer-natives)  
[Felnull Java library](https://github.com/TeamFelnull/FelNullJavaLibrary)  
[Java Youtube Downloader](https://github.com/sealedtx/java-youtube-downloader)  
[Mp3agic](https://github.com/mpatric/mp3agic)  
etc..

# Download

[Github](https://github.com/Mod-Sauce/IamMusicPlayer_FIX/releases/)                                                 
[Modrinth](https://modrinth.com/mod/iam-music-player-renewed) (Recommended)   
[Curseforge](https://www.curseforge.com/minecraft/mc-mods/iammusicplayer-renewed)

# Attention

Please note than none of us knows how to code Java
