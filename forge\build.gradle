plugins {
    id "com.github.johnrengelman.shadow" version "8.1.1"
//    id 'com.matthewprenger.cursegradle' version '1.4.0'
//    id "com.modrinth.minotaur" version "2.8.0"
}

architectury {
    platformSetupLoomIde()
    forge()
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    forge {
        convertAccessWideners = true
        extraAccessWideners.add loom.accessWidenerPath.get().asFile.name

        mixinConfig "iammusicplayer-common.mixins.json"

        dataGen {
            mod 'iammusicplayer'
        }
    }

    /*launches {
        data {
            arg "--existing", file("src/main/resources").absolutePath, "--existing", file("../common/src/main/resources").absolutePath
        }
    }*/
}

configurations {
    common
    shadowCommon
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentForge.extendsFrom common
    forgeDependencies.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}

dependencies {
    forge "net.minecraftforge:forge:${rootProject.forge_version}"

    modApi "dev.architectury:architectury-forge:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionForge")) { transitive = false }

    modApi "org.modsauce:otyacraftenginerenewed-forge:${rootProject.oe_version}"

    modApi "me.shedaniel.cloth:cloth-config-forge:${rootProject.cloth_config_version}"
    modRuntimeOnly "me.shedaniel:RoughlyEnoughItems-forge:${rootProject.rei_version}"

//    modApi "curse.maven:ctm-535489:${rootProject.sound_physics_remastered_forge}"

    shadowIn("dev.arbjerg:lavaplayer:${rootProject.lava_version_youtube}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
    }

    shadowIn "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"
    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.mpatric:mp3agic:0.9.1'

    shadowIn("dev.lavalink.youtube:v2:${rootProject.lavalink}")
}

processResources {
    inputs.property "version", project.version

    filesMatching("META-INF/mods.toml") {
        expand "version": project.version
    }
}

shadowJar {
    configurations = [project.configurations.shadowCommon]
    exclude "fabric.mod.json"
    exclude "architectury.common.json"
    archiveClassifier.set("dev-shadow")

    relocate 'org.json', 'dev.felnull.imp.include.org.json'
    relocate 'org.slf4j', 'dev.felnull.imp.include.org.slf4j'
    relocate 'certificates', 'dev.felnull.imp.include.certificates'
    relocate 'com.fasterxml', 'dev.felnull.imp.include.com.fasterxml'
    relocate 'org.jsoup', 'dev.felnull.imp.include.org.jsoup'
    relocate 'natives', 'dev.felnull.imp.include.natives'
    relocate 'mozilla', 'dev.felnull.imp.include.mozilla'
    relocate 'net.iharder', 'dev.felnull.imp.include.net.iharder'
    relocate 'com.sedmelluq.lava', 'dev.felnull.imp.include.com.sedmelluq.lava'
    relocate 'org.apache.http', 'dev.felnull.imp.include.org.apache.http'
    relocate('org.apache.commons', 'dev.felnull.imp.include.org.apache.commons') {
        include 'org.apache.commons.logging.**'
        include 'org.apache.commons.io.**'
        include 'org.apache.commons.codec.**'
    }
    relocate 'com.github', 'dev.felnull.imp.include.com.github'
    relocate 'com.alibaba', 'dev.felnull.imp.include.com.alibaba'
    relocate 'dev.felnull.fnjl', 'dev.felnull.imp.include.dev.felnull.fnjl'
    relocate 'com.mpatric', 'dev.felnull.imp.include.com.mpatric'
    relocate('com.sedmelluq.discord.lavaplayer', 'dev.felnull.imp.include.com.sedmelluq.discord.lavaplayer') {
        exclude 'com.sedmelluq.discord.lavaplayer.natives.**'
    }

    mergeServiceFiles {
        relocate 'javax.ws.rs.ext', 'dev.felnull.imp.include.javax.ws.rs.ext'
        // relocate 'javax.script.ScriptEngineFactory', 'dev.felnull.imp.include.javax.script.ScriptEngineFactory'
        relocate 'org.glassfish.jersey.internal.spi', 'dev.felnull.imp.include.org.glassfish.jersey.internal.spi'
    }

    relocate 'ibxm', 'dev.felnull.imp.include.ibxm'
    relocate 'net.sourceforge.jaad', 'dev.felnull.imp.include.net.sourceforge.jaad'
    relocate 'org.mozilla.classfile', 'dev.felnull.imp.include.org.mozilla.classfile'
    relocate 'dev.lavalink.youtube', 'dev.felnull.imp.include.dev.lavalink.youtube'
}

remapJar {
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier.set(null)
}

jar {
    archiveClassifier.set("dev")
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}
/*
publishing {
    publications {
        mavenForge(MavenPublication) {
            artifactId = rootProject.archives_base_name + "-" + project.name
            from components.java
            pom {
                name = 'IamMusicPlayer'
                description = 'The ikisugi music player mod.'
                licenses {
                    license {
                        name = 'GNU Lesser General Public License v3.0'
                        url = 'https://www.gnu.org/licenses/lgpl-3.0.txt'
                    }
                }
                developers {
                    developer {
                        id = 'MORIMORI0317'
                        name = 'MORIMORI0317'
                    }
                    developer {
                        id = 'FelNull'
                        name = 'TeamFelNull'
                        email = '<EMAIL>'
                    }
                }
            }
        }
    }

    repositories {
        maven {
            url "${project.maven_put_url}"
            credentials {
                username = "felnull"
                password = "${project.maven_put_pass}" != '' ? "${project.maven_put_pass}" : System.getenv('mavenpassword')
            }
        }
    }
}

curseforge {
    if (System.getenv('curesforgeapikey') != null && "${project.curesforge_id}" != '') {
        apiKey = System.getenv('curesforgeapikey')
        project {
            id = "${rootProject.curesforge_id}"

            changelogType = 'markdown'
            changelog = file('../LATEST_CHANGELOG.md')

            releaseType = "${rootProject.release_type}"
            addGameVersion "${rootProject.minecraft_version}"
            addGameVersion 'Java 17'
            addGameVersion "Forge"

            project.support_versions.split(",").each {
                String version -> addGameVersion version
            }

            relations {
                requiredDependency "cloth-config"
                requiredDependency "architectury-api"
                requiredDependency "otyacraft-engine"
                //    optionalDependency "patchouli"
            }

            mainArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Forge"
            }

            addArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}-sources.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Forge-sources"
            }
        }
    }
}

if (System.getenv('modrinthapikey') != null && "${modrinth_id}" != '') {
    modrinth {
        token = System.getenv('modrinthapikey')
        projectId = "${rootProject.modrinth_id}"
        versionName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Forge"
        versionNumber = "${rootProject.minecraft_version}-${project.mod_version}-Forge"
        versionType = "${project.release_type}"
        uploadFile = file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")
        gameVersions = List.of(project.support_versions.split(","))
        loaders = ["forge"]
        dependencies {
            required.project("9s6osm5g")//Cloth Config API
            required.project("lhGA9TYQ")//Architectury API
            required.project("iu3upNjC")//Otyacraft Engine
            //  optional.project("nU0bVIaL")//Patchouli
        }
        changelog = file('../LATEST_CHANGELOG.md').text
    }
}
*/