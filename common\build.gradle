architectury {
    common(rootProject.enabled_platforms.split(","))
}

loom {
    accessWidenerPath = file("src/main/resources/iammusicplayer.accesswidener")
}

dependencies {
    // We depend on fabric loader here to use the fabric @Environment annotations and get the mixin dependencies
    // Do NOT use other classes from fabric loader
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    // Remove the next line if you don't want to depend on the API
    modApi "dev.architectury:architectury:${rootProject.architectury_version}"

    modApi "${rootProject.oe_repo_pre}dev.felnull:otyacraftengine:${rootProject.oe_version}"
    modApi "me.shedaniel.cloth:cloth-config:8.3.115"

    implementation "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
/*    implementation("com.github.walkyst:lavaplayer-fork:${rootProject.lava_version}") {
        exclude group: 'com.sedmelluq', module: 'lavaplayer-natives'
    }
    implementation 'com.github.sealedtx:java-youtube-downloader:3.1.0'*/

    implementation("dev.arbjerg:lavaplayer:${rootProject.lava_version}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    implementation('com.fasterxml.jackson.core:jackson-core:2.14.3')
    implementation('com.fasterxml.jackson.core:jackson-databind:2.14.3')
    implementation 'com.github.sealedtx:java-youtube-downloader:3.2.3'

    implementation 'dev.lavalink.youtube:v2:1.13.1'

    implementation 'com.mpatric:mp3agic:0.9.1'
}

publishing {
    publications {
        mavenCommon(MavenPublication) {
            artifactId = rootProject.archives_base_name
            from components.java
            pom {
                name = 'IamMusicPlayer'
                description = 'The ikisugi music player mod.'
                licenses {
                    license {
                        name = 'GNU Lesser General Public License v3.0'
                        url = 'https://www.gnu.org/licenses/lgpl-3.0.txt'
                    }
                }
                developers {
                    developer {
                        id = 'MORIMORI0317'
                        name = 'MORIMORI0317'
                    }
                    developer {
                        id = 'FelNull'
                        name = 'TeamFelNull'
                        email = '<EMAIL>'
                    }
                }
            }
        }
    }
}
