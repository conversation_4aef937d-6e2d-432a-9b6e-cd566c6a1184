name: Mod Build
on: [ push ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 200
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: zulu
          cache: gradle
      - name: Loom Cache
        uses: actions/cache@v4
        with:
          path: "**/.gradle/loom-cache"
          key: "${{ runner.os }}-gradle-${{ hashFiles('**/libs.versions.*', '**/*.gradle*', '**/gradle-wrapper.properties') }}"
          restore-keys: "${{ runner.os }}-gradle-"
      - uses: gradle/actions/wrapper-validation@v4
      - run: chmod +x ./gradlew
      - name: Build with Gradle
        run: ./gradlew build
      - name: Read minecraft version from gradle.properties
        id: read-version
        run: |
          minecraft_version=$(grep '^minecraft_version=' gradle.properties | cut -d'=' -f2)
          echo "minecraft_version=$minecraft_version" >> $GITHUB_ENV
      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: IamMusicPlayerRenewed
          path: build/libs
