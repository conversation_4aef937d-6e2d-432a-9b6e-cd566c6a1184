plugins {
    id "com.github.johnrengelman.shadow" version "7.1.2"
    id 'com.matthewprenger.cursegradle' version '1.4.0'
    id "com.modrinth.minotaur" version "2.4.4"
}

architectury {
    platformSetupLoomIde()
    fabric()
}

sourceSets {
    main {
        resources {
            srcDirs += [
                    'src/main/generated'
            ]
        }
    }
}

loom {
    accessWidenerPath = project(":common").loom.accessWidenerPath

    runs {
        datagen {
            server()

            name "Minecraft Data"
            vmArg "-Dfabric-api.datagen"
            vmArg "-Dfabric-api.datagen.output-dir=${file("src/main/generated")}"

            runDir "run"
        }
    }
}
assemble.dependsOn runDatagen

configurations {
    common
    shadowCommon // Don't use shadow from the shadow plugin because we don't want IDEA to index this.
    compileClasspath.extendsFrom common
    runtimeClasspath.extendsFrom common
    developmentFabric.extendsFrom common
    implementation.extendsFrom shadowIn
    shadowCommon.extendsFrom shadowIn
}
repositories {
    maven { url "https://maven.terraformersmc.com/releases/" }//modmenu
    mavenCentral()
    maven { url "https://jitpack.io" } // For com.github.walkyst.JAADec-fork:jaadec-ext-aac & ibxm-fork:com.github.walkyst:ibxm-fork
}
dependencies {
    modImplementation "net.fabricmc:fabric-loader:${rootProject.fabric_loader_version}"
    modApi("net.fabricmc.fabric-api:fabric-api:${rootProject.fabric_api_version}")
    // Remove the next line if you don't want to depend on the API
    modApi "dev.architectury:architectury-fabric:${rootProject.architectury_version}"

    common(project(path: ":common", configuration: "namedElements")) { transitive false }
    shadowCommon(project(path: ":common", configuration: "transformProductionFabric")) { transitive false }

    modApi "${rootProject.oe_repo_pre}dev.felnull:otyacraftengine-fabric:${rootProject.oe_version}"
    modApi "com.terraformersmc:modmenu:4.0.6"
    modApi "me.shedaniel.cloth:cloth-config-fabric:8.3.115"
    modApi "me.shedaniel:RoughlyEnoughItems-fabric:9.1.528"
    modApi "vazkii.patchouli:Patchouli:1.19.2-76-FABRIC"
    modApi "curse.maven:ctm-535489:3949790"

/*    shadowIn("com.github.walkyst:lavaplayer-fork:${rootProject.lava_version}") {
        exclude group: 'com.github.walkyst', module: 'lavaplayer-natives-fork'
    }
    shadowIn 'com.github.sealedtx:java-youtube-downloader:3.1.0'*/

    shadowIn("dev.arbjerg:lavaplayer:${rootProject.lava_version_youtube}") {
        exclude group: 'dev.arbjerg', module: 'lavaplayer-natives'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-core'
        exclude group: 'com.fasterxml.jackson.core', module: 'jackson-databind'
    }
    shadowIn('com.fasterxml.jackson.core:jackson-core:2.14.3')
    shadowIn('com.fasterxml.jackson.core:jackson-databind:2.14.3')
    shadowIn "com.github.sealedtx:java-youtube-downloader:${rootProject.ytdownloader}"

    shadowIn("dev.lavalink.youtube:v2:1.13.1")
    
    shadowIn "dev.felnull:felnull-java-library:${rootProject.felnull_version}"
    shadowIn 'com.mpatric:mp3agic:0.9.1'

}

processResources {
    inputs.property "version", project.version

    filesMatching("fabric.mod.json") {
        expand "version": project.version
    }
}
shadowJar {
    configurations = [project.configurations.shadowIn]
    relocate 'org.json', 'dev.felnull.imp.include.org.json'
    relocate 'org.slf4j', 'dev.felnull.imp.include.org.slf4j'
    relocate 'certificates', 'dev.felnull.imp.include.certificates'
    relocate 'com.fasterxml', 'dev.felnull.imp.include.com.fasterxml'
    relocate 'org.jsoup', 'dev.felnull.imp.include.org.jsoup'
    relocate 'natives', 'dev.felnull.imp.include.natives'
    relocate 'mozilla', 'dev.felnull.imp.include.mozilla'
    relocate 'net.iharder', 'dev.felnull.imp.include.net.iharder'
    relocate 'com.sedmelluq.lava', 'dev.felnull.imp.include.com.sedmelluq.lava'
    relocate 'org.apache.http', 'dev.felnull.imp.include.org.apache.http'
    relocate('org.apache.commons', 'dev.felnull.imp.include.org.apache.commons') {
        include 'org.apache.commons.logging.**'
        include 'org.apache.commons.io.**'
        include 'org.apache.commons.codec.**'
    }
    relocate 'com.github', 'dev.felnull.imp.include.com.github'
    relocate 'com.alibaba', 'dev.felnull.imp.include.com.alibaba'
    relocate 'dev.felnull.fnjl', 'dev.felnull.imp.include.dev.felnull.fnjl'
    relocate 'com.mpatric', 'dev.felnull.imp.include.com.mpatric'
    relocate('com.sedmelluq.discord.lavaplayer', 'dev.felnull.imp.include.com.sedmelluq.discord.lavaplayer') {
        exclude 'com.sedmelluq.discord.lavaplayer.natives.**'
    }

    mergeServiceFiles {
        relocate 'javax.ws.rs.ext', 'dev.felnull.imp.include.javax.ws.rs.ext'
        //   relocate 'javax.script.ScriptEngineFactory', 'dev.felnull.imp.include.javax.script.ScriptEngineFactory'
        relocate 'org.glassfish.jersey.internal.spi', 'dev.felnull.imp.include.org.glassfish.jersey.internal.spi'
    }

    relocate 'ibxm', 'dev.felnull.imp.include.ibxm'
    relocate 'net.sourceforge.jaad.aac', 'dev.felnull.imp.include.net.sourceforge.jaad.aac'
    relocate 'org.mozilla.javascript', 'dev.felnull.imp.include.org.mozilla.javascript'
    relocate 'org.mozilla.classfile', 'dev.felnull.imp.include.org.mozilla.classfile'
}
shadowJar {
    exclude "architectury.common.json"

    configurations = [project.configurations.shadowCommon]
    archiveClassifier = "dev-shadow"
}

remapJar {
    injectAccessWidener = true
    input.set shadowJar.archiveFile
    dependsOn shadowJar
    archiveClassifier = null
    setArchivesBaseName("${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}")
}

jar {
    archiveClassifier = "dev"
}

sourcesJar {
    def commonSources = project(":common").sourcesJar
    dependsOn commonSources
    from commonSources.archiveFile.map { zipTree(it) }
}

components.java {
    withVariantsFromConfiguration(project.configurations.shadowRuntimeElements) {
        skip()
    }
}

publishing {
    publications {
        mavenFabric(MavenPublication) {
            artifactId = rootProject.archives_base_name + "-" + project.name
            from components.java
            pom {
                name = 'IamMusicPlayer'
                description = 'The ikisugi music player mod.'
                licenses {
                    license {
                        name = 'GNU Lesser General Public License v3.0'
                        url = 'https://www.gnu.org/licenses/lgpl-3.0.txt'
                    }
                }
                developers {
                    developer {
                        id = 'MORIMORI0317'
                        name = 'MORIMORI0317'
                    }
                    developer {
                        id = 'FelNull'
                        name = 'TeamFelNull'
                        email = '<EMAIL>'
                    }
                }
            }
        }
    }
}

curseforge {
    if (System.getenv('curesforgeapikey') != null && "${project.curesforge_id}" != '') {
        apiKey = System.getenv('curesforgeapikey')
        project {
            id = "${rootProject.curesforge_id}"

            changelogType = 'markdown'
            changelog = file('../LATEST_CHANGELOG.md')

            releaseType = "${rootProject.release_type}"
            addGameVersion "${rootProject.minecraft_version}"
            addGameVersion 'Java 17'
            addGameVersion "Fabric"
            addGameVersion "Quilt"

            project.support_versions.split(",").each {
                String version -> addGameVersion version
            }

            relations {
                requiredDependency "fabric-api"
                requiredDependency "cloth-config"
                requiredDependency "architectury-api"
                requiredDependency "otyacraft-engine"
                optionalDependency "patchouli-fabric"
            }

            mainArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric"
            }

            addArtifact(file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}-sources.jar")) {
                displayName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric-sources"
            }
            afterEvaluate {
                uploadTask.dependsOn("build")
            }
        }
    }
    options {
        forgeGradleIntegration = false
    }
}

if (System.getenv('modrinthapikey') != null && "${modrinth_id}" != '') {
    modrinth {
        token = System.getenv('modrinthapikey')
        projectId = "${rootProject.modrinth_id}"
        versionName = "${rootProject.mod_display_name}-${rootProject.minecraft_version}-${project.mod_version}-Fabric"
        versionNumber = "${rootProject.minecraft_version}-${project.mod_version}-Fabric"
        versionType = "${project.release_type}"
        uploadFile = file("${project.buildDir}/libs/${rootProject.archives_base_name}-${project.name}-mc${rootProject.minecraft_version}-${project.mod_version}.jar")
        gameVersions = List.of(project.support_versions.split(","))
        loaders = ["fabric", "quilt"]
        dependencies {
            required.project("P7dR8mSH")//Fabric API
            required.project("9s6osm5g")//Cloth Config API
            required.project("lhGA9TYQ")//Architectury API
            required.project("iu3upNjC")//Otyacraft Engine
            optional.project("nU0bVIaL")//Patchouli
        }
        changelog = file('../LATEST_CHANGELOG.md').text
    }
}
