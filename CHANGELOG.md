# Changelog
Changelog to track updates for this mod.  
    Add your changes to Unreleased if you want to commit.  
    Please write according to [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)

## [Unreleased]

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [3.22.0-alpha.5] - 2024-05-19

### Fixed
- Fixed the problem that YouTube cannot be played

## [3.22.0-alpha.4] - 2024-03-13

### Fixed
- Fixed the problem that YouTube cannot be played

## [3.22.0-alpha.3] - 2024-01-24

### Added
- Added config to hide display sprites and decorative antennas

## [3.22.0-alpha.2] - 2023-09-08

### Fixed
- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)

## [3.22.0-alpha.1] - 2023-07-01

### Changed
- Port MC1.20

## [3.21.4] - 2023-06-13

### Added
- Re-support Patchouli

## [3.21.3] - 2023-04-28

### Changed
- Updated LavaPlayer version to 1.4

### Fixed
- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)

## [3.21.2] - 2023-04-01

### Added
- AprilFool Antenna

### Changed
- Update SpecialModelLoader

## [3.21.1] - 2023-03-27

### Fixed
- Fixed problems with recipes, etc. not being applied.

## [3.21.0] - 2023-03-27

### Added
- Add parabolic antenna craft recipe

## [3.21.0-alpha.1] - 2023-03-22

### Changed
- Port MC1.19.4

## [3.20.1] - 2023-02-14

### Added
- Re-support Patchouli

## [3.20.0] - 2023-01-25

### Fixed
- Fixed an issue where the boombox could not be played as a dropped item
- Fixed parrots not dancing

## [3.20.0-beta.1] - 2023-01-22

### Changed
- Port MC1.19.3
- Embed OBJ loader (Fabric Only)

### Fixed
- Fixed a problem that crashes without notifying when ClothConfig is not installed (Forge Only)
- Fixed a bug that could occur when reloading while playing music
- Open mymusic and mypictuer initially when selecting music or images

## [3.20.0-alpha.2] - 2023-01-20

### Changed
- Port MC1.19.3
- Embed OBJ loader (Fabric Only)

### Fixed
- Open mymusic and mypictuer initially when selecting music or images

## [3.20.0-alpha.1] - 2023-01-19

### Changed
- Port MC1.19.3
- Embed OBJ loader (Fabric Only)

[Unreleased]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.22.0-alpha.5...HEAD
[3.22.0-alpha.4]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.22.0-alpha.3...v3.22.0-alpha.4
[3.22.0-alpha.2]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.22.0-alpha.1...v3.22.0-alpha.2
[3.22.0-alpha.1]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.4...v3.22.0-alpha.1
[3.22.0-alpha.3]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.22.0-alpha.2...v3.22.0-alpha.3
[3.22.0-alpha.5]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.22.0-alpha.4...v3.22.0-alpha.5
[3.21.4]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.3...v3.21.4
[3.21.3]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.2...v3.21.3
[3.21.2]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.1...v3.21.2
[3.21.1]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.0...v3.21.1
[3.21.0]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.21.0-alpha.1...v3.21.0
[3.21.0-alpha.1]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.20.1...v3.21.0-alpha.1
[3.20.1]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.20.0...v3.20.1
[3.20.0]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.20.0-beta.1...v3.20.0
[3.20.0-alpha.1]: https://github.com/TeamFelnull/IamMusicPlayer/commits/v3.20.0-alpha.1
[3.20.0-alpha.2]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.20.0-alpha.1...v3.20.0-alpha.2
[3.20.0-beta.1]: https://github.com/TeamFelnull/IamMusicPlayer/compare/v3.20.0-alpha.2...v3.20.0-beta.1
