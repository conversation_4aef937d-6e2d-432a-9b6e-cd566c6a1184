{"_comment.i": "<PERSON><PERSON>", "item.iammusicplayer.radio_antenna": "Радио антена", "item.iammusicplayer.radio_antenna.desc": "Требуется для прослушивания радиопотоков", "item.iammusicplayer.parabolic_antenna": "Параболическая антенна", "item.iammusicplayer.parabolic_antenna.desc": "Требуется для прослушивания без использования кассет", "item.iammusicplayer.cassette_tape": "Кассета", "item.iammusicplayer.cassette_tape_glass": "Стеклянная кассетная лента", "item.iammusicplayer.cassette_tape.written": "Кассета с записью", "item.iammusicplayer.cassette_tape_glass.written": "Стеклянная кассета с записью", "item.iammusicplayer.manual": "Руководство по эксплуатации музыкального плеера Iam", "item.iammusicplayer.invalid_item.patchouli.desc": "<PERSON><PERSON>li должен быть установлен", "item.iammusicplayer.invalid_item.patchouli.config.desc": "Интеграция с Patchouli должна быть включена в конфиге.", "item.iammusicplayer.invalid_item.desc": "Этот элемент недействителен!", "_comment.bl": "Block", "block.iammusicplayer.music_manager": "Музыкальный менеджер", "block.iammusicplayer.cassette_deck": "Кассетная дека", "block.iammusicplayer.boombox": "Бумбокс", "_comment.g": "GUI", "imp.monitor.music_manager.test": "Test", "imp.monitor.music_manager.play_list": "Плейлист", "imp.monitor.music_manager.add_play_list": "Добавить плейлист", "imp.monitor.music_manager.create_play_list": "Создать плейлист", "imp.monitor.music_manager.add_music": "Добавить музыку", "imp.monitor.music_manager.search_music": "Поиск музыки", "imp.monitor.music_manager.upload_music": "Загрузить музыку", "imp.monitor.music_manager.add_online_play_list": "Добавить онлайн плейлист", "imp.monitor.music_manager.import_youtube_play_list": "Импорт плейлиста ютуба", "imp.monitor.music_manager.detail_play_list": "Детали списка воспроизведения", "imp.monitor.music_manager.edit_play_list": "Изменить плейлист", "imp.monitor.music_manager.import_play_list_select": "Импорт плейлиста: выбрать", "imp.monitor.music_manager.detail_music": "Детали музыки", "imp.monitor.music_manager.edit_music": "Редактировать музыку", "imp.monitor.music_manager.delete_play_list": "Удалить плейлист", "imp.monitor.music_manager.delete_music": "Удалить музыку", "imp.monitor.music_manager.import_musics_select": "Импорт музыки: выбрать", "imp.monitor.music_manager.import_youtube_play_list_musics": "Импорт музыки из плейлиста YouTube", "imp.monitor.music_manager.authority": "Authority", "imp.monitor.boombox.off": "Выключенние", "imp.monitor.boombox.playing": "Игр<PERSON><PERSON><PERSON>", "imp.monitor.boombox.radio": "Радио", "imp.button.power": "Мощность", "imp.button.close": "Закрыть", "imp.button.backScreen": "Вернуться к предыдущему экрану", "imp.button.sort": "Сортировать", "imp.button.addPlaylist": "Добавить плейлист", "imp.button.addMusic": "Добавить музыку", "imp.button.createPlaylist": "Создать плейлист", "imp.button.addOnlinePlaylist": "Добавить плейлист онлайн", "imp.button.imageSet.file_open": "Открыть файл", "imp.button.imageSet.player_face": "Значок лица игрока", "imp.button.imageSet.delete": "Удалить", "imp.button.imageSet.url": "Укажите URL-адрес", "imp.button.back": "Назад", "imp.button.create": "Создать", "imp.button.add": "Добавить", "imp.button.save": "Сохранить", "imp.button.addInvitePlayer": "Добавить приглашенного игрока", "imp.button.playbackControl": "Управление воспроизведением", "imp.button.search": "Поиск", "imp.button.uploadMusic": "Загрузить трек", "imp.button.openFile": "Открыть файл", "imp.button.write": "Игр<PERSON><PERSON><PERSON>", "imp.button.playback": "Воспроизведение", "imp.button.writeStart": "Напис<PERSON><PERSON>ь Старт", "imp.button.boombox.none": "None", "imp.button.boombox.power": "Мощность", "imp.button.boombox.radio": "Радио", "imp.button.boombox.start": "Воспроизвести", "imp.button.boombox.pause": "Пауза", "imp.button.boombox.stop": "Остановить", "imp.button.boombox.loop": "Реплей", "imp.button.boombox.volDown": "Убавить звук", "imp.button.boombox.volUp": "Увеличить громкость", "imp.button.boombox.volMute": "Отключение звука", "imp.button.boombox.volMax": "Максимальный звук", "imp.button.importYoutubePlayList": "Импорт плейлиста ютуба", "imp.button.edit": "Редактировать", "imp.button.delete": "Удалить", "imp.button.exit": "Выход", "imp.button.detailPlaylist": "Детали", "imp.button.import": "Импорт", "imp.button.authority": "Authority", "imp.button.expulsion": "Expulsion", "imp.button.radioStreamStart": "Запуск радио волны", "imp.progressBar.playbackControl": "Полоса прогресса управления воспроизведением", "imp.radioButton.public": "Публичный", "imp.radioButton.private": "Приватный", "imp.radioButton.readonly": "Только просмотр", "imp.radioButton.member": "Участник", "imp.radioButton.ban": "<PERSON>ан", "imp.radioButton.admin": "Администратор", "imp.fixedList.myPlaylist": "Мой плейлист", "imp.fixedList.joinPlaylist": "Присоединяйтесь к списку воспроизведения", "imp.fixedList.onlinePlayers": "Онлайн игроки", "imp.fixedList.invitePlayers": "Пригласить игрока(ов)", "imp.fixedList.musicLoaderTypes": "Типы музыкальных загрузчиков", "imp.fixedList.searchMusic": "Поиск музыки", "imp.fixedList.musics": "Музыка", "imp.fixedList.memberPlayers": "Игроки-участники", "imp.fixedList.youtubePlayListMusics": "Музыка из плейлиста ютуба", "imp.fixedList.authorityPlayers": "Authority players", "imp.sortType.name": "Сортировка имени", "imp.sortType.player": "Сортировка имени игрока(ов)", "imp.sortType.create_date": "Создать сортировку по дате", "imp.orderType.ascending": "Восходящий", "imp.orderType.descending": "По убыванию", "imp.editBox.imageUrl": "URL изображения", "imp.editBox.name": "Название", "imp.editBox.invitePlayerByName": "Пригласить игрока по имени", "imp.editBox.musicSourceName": "Название источника музыки", "imp.editBox.musicSearchName": "Название музыкального поиска", "imp.editBox.youtubePlaylistIdentifier": "Идентификатор плейлиста Youtube", "imp.editBox.radioUrl": "Адрес радио", "imp.widget.volume": "Громкость", "imp.widget.playBackControl": "Управление воспроизведением", "imp.widget.loopControl": "Петля управления", "imp.widget.playProgressControl": "Управление ходом игры", "imp.widget.continuousControl": "Непрерывный контроль", "imp.text.playerCount": "%s Игрок(ов)", "imp.text.musicCount": "%s Музыкальных композиций", "imp.text.invitation": "Приглашение", "imp.text.addPlaylistInfo": "%s плейлистов %s игроков всего %s музыки", "imp.text.playlistInfo": "%s плейлистов %s музыки", "imp.text.public": "Публичный", "imp.text.image": "Изображение", "imp.text.noImage": "Нет изображения", "imp.text.dropInfo": "Перетащите, чтобы установить изображение", "imp.text.imageLoad.empty": "Пожалуйста, введите URL", "imp.text.imageLoad.error": "Ошибка загрузки!: %s", "imp.text.imageLoad.notImageUrl": "Что-то пошло не так с URL изображения", "imp.text.imageLoad.loadingImage": "Loading image...", "imp.text.imageLoad.optimizationImage": "Optimizing image...", "imp.text.imageLoad.notImage": "Not an image", "imp.text.imageLoad.uploadImage": "Uploading image...", "imp.text.imageLoad.tooManyImages": "Too many images", "imp.text.imageLoad.fileNotFound": "Image not found", "imp.text.imageLoad.uploadFailure": "Upload failed, please try again: %s", "imp.text.imageLoad.directory": "Directory", "imp.text.name": "Name", "imp.text.notEntered": "%s have not been entered", "imp.text.publishingSettings": "Publishing settings", "imp.text.initialAuthority": "Initial authority", "imp.text.invite": "Invite", "imp.text.uninvited": "Uninvited", "imp.text.invited": "Invited", "imp.text.member": "Member", "imp.text.invitePlayerByMCIDOrUUID": "Invite player by mcid or uuid", "imp.text.playbackLoading": "Loading...", "imp.text.musicSource": "Music Source", "imp.text.musicChecking": "Checking...", "imp.text.musicGuessing": "Guessing...", "imp.text.searching": "Searching...", "imp.text.loaderTypeInfo.auto": "Guess which loader the string is", "imp.text.enterText.default": "Identifier", "imp.text.enterText.auto": "Identifiable String", "imp.text.enterText.youtube": "Video ID or Youtube URL", "imp.text.enterText.soundcloud": "Sound Cloud URL", "imp.text.enterText.neteasecloudmusic": "Song ID or Netease Cloud Music URL", "imp.text.enterText.url": "Music URL", "imp.text.loadFailure": "Could not load", "imp.text.loadFailure.auto": "Could not guess", "imp.text.musicAuthor": "Author: %s", "imp.text.relayServer": "Relay Server", "imp.text.relayServer.response": "Response Speed: %sms  Processing speed: %sms", "imp.text.relayServer.error": "Connection Failed: %s", "imp.text.relayServer.connectingChecking": "Connecting checking...", "imp.text.relayServer.uploadInfo": "Maximum file size of %s", "imp.text.relayServer.uploading": "Uploading file...", "imp.text.relayServer.warning": "Please do not upload important data such as confidential information!", "imp.text.relayServer.responsibility": "We are not responsible for any damage caused by uploading.", "imp.text.relayServer.how": "Uploading to Discord via a relay server.", "imp.text.uploadDropInfo": "Can upload file by drag and drop", "imp.text.fileUpload.tooManyFiles": "Too many files", "imp.text.fileUpload.fileNotFound": "File not found", "imp.text.fileUpload.error": "Upload Error!: %s", "imp.text.fileUpload.failure": "%s: %s", "imp.text.fileUpload.sizeOver": "<PERSON>ze over", "imp.text.fileUpload.directory": "Directory", "imp.text.fileUpload.noURL": "Empty URL", "imp.text.unknownPlayer": "Unknown", "imp.text.noAntenna": "<PERSON><PERSON><PERSON> is not set", "imp.text.noCassetteTape": "Please set the cassette tape", "imp.text.writing": "Writing...", "imp.text.noMusicCassetteTape": "No music has been written", "imp.text.musicLoading": "Loading music...", "imp.text.streamLoading": "Loading stream music...", "imp.text.streamPlaying": "Playing stream music...", "imp.text.playlistLoading": "Loading play list...", "imp.text.enterStream": "Enter radio stream or youtube live url", "imp.text.authority.owner": "Owner", "imp.text.authority.admin": "Admin", "imp.text.authority.member": "Member", "imp.text.authority.read_only": "Read only", "imp.text.authority.invitation": "Invitation", "imp.text.authority.ban": "Ban", "imp.text.authority.none": "None", "imp.text.importMusicCount": "%s Musics", "imp.text.importing": "Importing...", "imp.text.importFailure": "Import failed", "imp.text.deleteWarning": "%s' will be lost forever! (A long time!)", "imp.text.cantChangeAuthority": "You can't change authority", "imp.text.radioChecking": "Checking...", "imp.text.continuous.none": "None", "imp.text.continuous.order": "Order", "imp.text.continuous.random": "Random", "imp.text.manual": "Manual", "imp.text.manual.coverInfo": "Very easy to understand!", "imp.fileChooser.files.all": "All Files", "imp.loaderType.auto": "Auto", "imp.loaderType.upload": "Upload", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "Http URL", "imp.loaderType.soundcloud": "Sound Cloud", "imp.loaderType.neteasecloudmusic": "Netease Cloud Music", "imp.fileChooser.title.music": "Open music file", "imp.fileChooser.title.image": "Open image file", "imp.ringer.have": "%s that %s has", "imp.ringer.drop": "Dropped %s", "soundCategory.iammusicplayer": "IMP Music", "imp.button.config": "IMP Config", "_comment.co": "Config", "text.autoconfig.iammusicplayer.title": "Iam Music Player Config", "text.autoconfig.iammusicplayer.category.client": "Client Side", "text.autoconfig.iammusicplayer.option.errorLog": "<PERSON><PERSON><PERSON>", "text.autoconfig.iammusicplayer.option.volume": "Volume", "text.autoconfig.iammusicplayer.option.maxPlayCont": "Max music play cont", "text.autoconfig.iammusicplayer.option.spatial": "Spatial", "text.autoconfig.iammusicplayer.option.relayServerURL": "Relay server url", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "LavaPlayer native library url", "text.autoconfig.iammusicplayer.category.server": "Server Side", "text.autoconfig.iammusicplayer.option.maxWaitTime": "<PERSON> Wait Time", "text.autoconfig.iammusicplayer.option.retryTime": "Retry Time", "text.autoconfig.iammusicplayer.option.dropItemRing": "Whether to play in the dropped item", "text.autoconfig.iammusicplayer.category.integration": "Integration", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "Patchouli Integration", "_comment.cc": "Config Value", "subtitleType.off": "Off", "subtitleType.vanilla": "Vanilla", "subtitleType.overlay": "Overlay", "_comment.c": "Command", "commands.imp.ringer.info": "There are currently %s ringers and %s during playback (%s)", "commands.imp.ringer.info.all": "There are currently %s ringers and %s during playback", "commands.imp.ringer.list": "The existing ringer are as follows in %s", "commands.imp.ringer.list.all.notFound": "Ringer was not found", "commands.imp.ringer.list.notFound": "Ringer was not found in %s", "commands.imp.ringer.list.all": "The existing ringer are as follows", "commands.imp.ringer.list.entry.playing": "%s, exists at %s, and is playing", "commands.imp.ringer.list.entry": "%s, exists at %s", "commands.imp.ringer.list.all.entry.playing": "%s, exists at %s (%s), and is playing", "commands.imp.ringer.list.all.entry": "%s, exists at %s (%s)", "_comment.e": "Entity", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Advancements", "advancements.iammusicplayer.root.title": "<PERSON>am Music Player", "advancements.iammusicplayer.root.description": "The ikisugi music player mod", "advancements.iammusicplayer.add_music.title": "Music register", "advancements.iammusicplayer.add_music.description": "Add music to playlists using Music Manager", "advancements.iammusicplayer.write_cassette_tape.title": "Writing...", "advancements.iammusicplayer.write_cassette_tape.description": "Write music on cassette tapes using a cassette deck", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "I listen to music", "advancements.iammusicplayer.listen_to_radio.title": "A Live music", "advancements.iammusicplayer.listen_to_radio.description": "Listen to the radio stream", "advancements.iammusicplayer.listen_to_remote_music.title": "Listen directly", "advancements.iammusicplayer.listen_to_remote_music.description": "Play playlists directly using a parabolic antenna", "advancements.iammusicplayer.listen_to_kamesuta.title": "Kame Power!!!", "advancements.iammusicplayer.listen_to_kamesuta.description": "Play a video of a kamesuta using a kamesuta antenna", "_comment.bo": "Book", "book.imp.landing_text": "This book describes how to use IamMusicPlayer.$(br2)Please note that the English translation may be incorrect because the creator of this mod is Japanese.$(br2)If you find any bugs in this mod or conflicts with other mods, please let us know in the $(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub issues$(/l).", "_comment.cr": "CreativeTab", "itemGroup.iammusicplayer.iammusicplayer": "<PERSON>am Music Player", "_comment.other": "Other", "modmenu.descriptionTranslation.iammusicplayer": "Add a music player that you can listen to with multiple players.\nThe ikisugi music player mod...", "_comment.la": "Last"}