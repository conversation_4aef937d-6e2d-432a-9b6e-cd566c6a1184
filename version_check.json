{"homepage": "https://www.curseforge.com/minecraft/mc-mods/iammusicplayer", "1.19.2": {"3.17": "First 1.19.2", "3.19.7": "Added\n- Add parabolic antenna craft recipe\n", "3.19.8": "Fixed\n- Fixed problems with recipes, etc. not being applied.\n", "3.19.9": "Changed\n- Updated LavaPlayer version to 1.4\nFixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.19.10": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.19.11": "Added\n- Added config to hide display sprites and decorative antennas\n", "3.19.12": "Fixed\n- Fixed the problem that YouTube cannot be played\n"}, "1.18.2": {"3.5": "First 1.18.2", "3.16.3": "Changed\n- Updated LavaPlayer version to 1.4\nFixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.16.4": "Fixed\n- Fixed a bug that caused a conflict with some mods, causing them to not start and crash.\n", "3.16.5": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.16.6": "Fixed\n- Fixed the problem that YouTube cannot be played\n"}, "1.18.1": {"3.4": "Update OE version", "3.3": "Fix spi conflict", "3.2": "Fix bug", "3.1": "First 1.18.1"}, "1.17.1": {"3.0": "First 1.17.1"}, "1.16.5": {"2.8": "Added ru_ru language and update library version"}, "1.16.1": {"1.3": "The 1.16.1 Frist,Quite unstable.", "1.4": "Fix forge106 Crash"}, "1.15.2": {"1.2": "Resume music when boombox is stopped or broken\n-Simultaneously proceed downloading and listening\n-The One Probe is supported\n-Fix crash when Cassette Deck finishes recording on Mac or Linux", "1.1": "Name Change(Ikisugi Music Player -> Iam Music Player)\n-Other change iroiro.", "1.0": "The Frist Release."}, "promos": {"1.19.2-latest": "3.19.12", "1.19.2-recommended": "3.19.12", "1.18.2-latest": "3.16.6", "1.18.2-recommended": "3.16.6", "1.18.1-latest": "3.4", "1.18.1-recommended": "3.4", "1.17.1-latest": "3.0", "1.17.1-recommended": "3.0", "1.16.5-latest": "2.10", "1.16.5-recommended": "2.10", "1.16.1-latest": "1.4", "1.16.1-recommended": "1.4", "1.15.2-latest": "1.2", "1.15.2-recommended": "1.2", "1.19.3-latest": "3.20.3", "1.19.3-recommended": "3.20.3", "1.19.4-latest": "3.21.7", "1.19.4-recommended": "3.21.7", "1.20-latest": "3.22.0-alpha.5", "1.20-recommended": "3.22.0-alpha.5", "1.20.1-latest": "3.22.0-alpha.5", "1.20.1-recommended": "3.22.0-alpha.5"}, "1.19.3": {"3.20.0-alpha.1": "Changed\n- Port MC1.19.3\n- Embed OBJ loader (Fabric Only)\n", "3.20.0-alpha.2": "Changed\n- Port MC1.19.3\n- Embed OBJ loader (Fabric Only)\nFixed\n- Open mymusic and mypictuer initially when selecting music or images\n", "3.20.0-beta.1": "Changed\n- Port MC1.19.3\n- Embed OBJ loader (Fabric Only)\nFixed\n- Fixed a problem that crashes without notifying when ClothConfig is not installed (Forge Only)\n- Fixed a bug that could occur when reloading while playing music\n- Open mymusic and mypictuer initially when selecting music or images\n", "3.20.0": "Fixed\n- Fixed an issue where the boombox could not be played as a dropped item\n- Fixed parrots not dancing\n", "3.20.1": "Added\n- Re-support Patchouli\n", "3.20.2": "Changed\n- Updated LavaPlayer version to 1.4\nFixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.20.3": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n"}, "1.19.4": {"3.21.0-alpha.1": "Changed\n- Port MC1.19.4\n", "3.21.0": "Added\n- Add parabolic antenna craft recipe\n", "3.21.1": "Fixed\n- Fixed problems with recipes, etc. not being applied.\n", "3.21.2": "Added\n- AprilFool Antenna\nChanged\n- Update SpecialModelLoader\n", "3.21.3": "Changed\n- Updated LavaPlayer version to 1.4\nFixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.21.4": "Added\n- Re-support Patchouli\n", "3.21.5": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.21.6": "Added\n- Added config to hide display sprites and decorative antennas\n", "3.21.7": "Fixed\n- Fixed the problem that YouTube cannot be played\n"}, "1.20": {"3.22.0-alpha.1": "Changed\n- Port MC1.20\n", "3.22.0-alpha.2": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.22.0-alpha.3": "Added\n- Added config to hide display sprites and decorative antennas\n", "3.22.0-alpha.4": "Fixed\n- Fixed the problem that YouTube cannot be played\n", "3.22.0-alpha.5": "Fixed\n- Fixed the problem that YouTube cannot be played\n"}, "1.20.1": {"3.22.0-alpha.1": "Changed\n- Port MC1.20\n", "3.22.0-alpha.2": "Fixed\n- Fixed an issue where YouTube could not be played (Fixed by updating LavaPlayer version)\n", "3.22.0-alpha.3": "Added\n- Added config to hide display sprites and decorative antennas\n", "3.22.0-alpha.4": "Fixed\n- Fixed the problem that YouTube cannot be played\n", "3.22.0-alpha.5": "Fixed\n- Fixed the problem that YouTube cannot be played\n"}}