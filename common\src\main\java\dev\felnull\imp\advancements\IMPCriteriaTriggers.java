package dev.felnull.imp.advancements;

import net.minecraft.advancements.CriteriaTriggers;
import net.minecraft.advancements.critereon.SimpleCriterionTrigger;
import org.modsauce.otyacraftenginerenewed.advancement.ModInvolvementTrigger;

public class IMPCriteriaTriggers {
    public static final AddMusicTrigger ADD_MUSIC = new AddMusicTrigger();
    public static final WriteCassetteTapeTrigger WRITE_CASSETTE_TAPE = new WriteCassetteTapeTrigger();
    public static final ListenToMusicTrigger LISTEN_TO_MUSIC = new ListenToMusicTrigger();

    // External trigger from otyacraftenginerenewed
    public static final SimpleCriterionTrigger<ModInvolvementTrigger.TriggerInstance> MOD_INVOLVEMENT = new ModInvolvementTrigger();

    public static void init() {
        CriteriaTriggers.register("iammusicplayer:add_music", ADD_MUSIC);
        CriteriaTriggers.register("iammusicplayer:write_cassette_tape", WRITE_CASSETTE_TAPE);
        CriteriaTriggers.register("iammusicplayer:listen_to_music", LISTEN_TO_MUSIC);
    }
}
