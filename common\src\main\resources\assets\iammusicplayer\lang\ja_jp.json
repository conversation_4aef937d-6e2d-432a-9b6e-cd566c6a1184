{"_comment.i": "<PERSON><PERSON>", "item.iammusicplayer.radio_antenna": "ラジオアンテナ", "item.iammusicplayer.radio_antenna.desc": "ラジオストリームを聞くために必要", "item.iammusicplayer.parabolic_antenna": "パラボラアンテナ", "item.iammusicplayer.parabolic_antenna.desc": "カセットテープを使用せず音楽を聞くために必要", "item.iammusicplayer.cassette_tape": "カセットテープ", "item.iammusicplayer.cassette_tape_glass": "ガラスのカセットテープ", "item.iammusicplayer.cassette_tape.written": "書き込み済みカセットテープ", "item.iammusicplayer.cassette_tape_glass.written": "書き込み済みガラスのカセットテープ", "item.iammusicplayer.manual": "Iam Music Player 取扱説明書", "item.iammusicplayer.invalid_item.patchouli.desc": "Patchouliを導入する必要があります", "item.iammusicplayer.invalid_item.patchouli.config.desc": "コンフィグでPatchouliとの連携を有効にする必要があります", "item.iammusicplayer.invalid_item.desc": "このアイテムは無効です！", "_comment.bl": "Block", "block.iammusicplayer.music_manager": "音楽管理装置", "block.iammusicplayer.cassette_deck": "カセットデッキ", "block.iammusicplayer.boombox": "ラジカセ", "_comment.g": "GUI", "imp.monitor.music_manager.test": "Test", "imp.monitor.music_manager.play_list": "プレイリスト", "imp.monitor.music_manager.add_play_list": "プレイリストを追加", "imp.monitor.music_manager.create_play_list": "プレイリストを作成", "imp.monitor.music_manager.add_music": "音楽を追加", "imp.monitor.music_manager.search_music": "音楽を検索", "imp.monitor.music_manager.upload_music": "音楽をアップロード", "imp.monitor.music_manager.add_online_play_list": "オンラインからプレイリストを追加", "imp.monitor.music_manager.import_youtube_play_list": "Youtubeプレイリストをインポート", "imp.monitor.music_manager.detail_play_list": "プレイリストの詳細", "imp.monitor.music_manager.edit_play_list": "プレイリストを編集", "imp.monitor.music_manager.import_play_list_select": "プレイリストのインポートを選択", "imp.monitor.music_manager.detail_music": "音楽の詳細", "imp.monitor.music_manager.edit_music": "音楽を編集", "imp.monitor.music_manager.delete_play_list": "プレイリストを削除", "imp.monitor.music_manager.delete_music": "音楽を削除", "imp.monitor.music_manager.import_musics_select": "音楽のインポートを選択", "imp.monitor.music_manager.import_youtube_play_list_musics": "Youtubeプレイリストから音楽をインポート", "imp.monitor.music_manager.authority": "権限", "imp.monitor.boombox.off": "オフ", "imp.monitor.boombox.playing": "再生中", "imp.monitor.boombox.radio": "ラジオ", "imp.button.power": "電源", "imp.button.close": "閉じる", "imp.button.backScreen": "前の画面に戻る", "imp.button.sort": "並べ替え", "imp.button.addPlaylist": "プレイリスト追加", "imp.button.addMusic": "音楽を追加", "imp.button.createPlaylist": "プレイリストを作成", "imp.button.addOnlinePlaylist": "オンラインから追加", "imp.button.imageSet.file_open": "ファイルを開く", "imp.button.imageSet.player_face": "プレイヤーの顔アイコン", "imp.button.imageSet.delete": "削除", "imp.button.imageSet.url": "URLを指定", "imp.button.back": "戻る", "imp.button.create": "作成", "imp.button.add": "追加", "imp.button.save": "保存", "imp.button.addInvitePlayer": "プレイヤーを招待", "imp.button.playbackControl": "再生コントロール", "imp.button.search": "検索", "imp.button.uploadMusic": "音楽をアップロード", "imp.button.openFile": "ファイルを開く", "imp.button.write": "書き込み", "imp.button.playback": "再生", "imp.button.writeStart": "書き込み開始", "imp.button.boombox.none": "無効", "imp.button.boombox.power": "電源", "imp.button.boombox.radio": "ラジオ", "imp.button.boombox.start": "再生", "imp.button.boombox.pause": "一時停止", "imp.button.boombox.stop": "停止", "imp.button.boombox.loop": "ループ", "imp.button.boombox.volDown": "音量を下げる", "imp.button.boombox.volUp": "音量を上げる", "imp.button.boombox.volMute": "ミュート", "imp.button.boombox.volMax": "音量を最大化", "imp.button.importYoutubePlayList": "Youtubeプレイリストをインポート", "imp.button.edit": "編集", "imp.button.delete": "削除", "imp.button.exit": "退出", "imp.button.detailPlaylist": "詳細", "imp.button.import": "インポート", "imp.button.authority": "権限", "imp.button.expulsion": "追放", "imp.button.radioStreamStart": "ラジオストリーム開始", "imp.progressBar.playbackControl": "再生コントロール進捗率バー", "imp.radioButton.public": "公開", "imp.radioButton.private": "非公開", "imp.radioButton.readonly": "読み込み専用", "imp.radioButton.member": "メンバー", "imp.radioButton.ban": "禁止", "imp.radioButton.admin": "管理者", "imp.fixedList.myPlaylist": "自分のプレイリスト", "imp.fixedList.joinPlaylist": "参加可能なプレイリスト", "imp.fixedList.onlinePlayers": "オンラインのプレイヤー", "imp.fixedList.invitePlayers": "招待済みプレイヤー", "imp.fixedList.musicLoaderTypes": "音楽読み込みタイプ", "imp.fixedList.searchMusic": "音楽を検索", "imp.fixedList.musics": "音楽", "imp.fixedList.memberPlayers": "メンバーのプレイヤー", "imp.fixedList.youtubePlayListMusics": "Youtubeプレイリストの音楽", "imp.fixedList.authorityPlayers": "プレイヤーの権限", "imp.sortType.name": "名前順", "imp.sortType.player": "プレイヤー名順", "imp.sortType.create_date": "作成日順", "imp.orderType.ascending": "昇順", "imp.orderType.descending": "降順", "imp.editBox.imageUrl": "画像URL", "imp.editBox.name": "名前", "imp.editBox.invitePlayerByName": "プレイヤー名から招待", "imp.editBox.musicSourceName": "音楽元URL", "imp.editBox.musicSearchName": "音楽検索", "imp.editBox.youtubePlaylistIdentifier": "YoutubeプレイリストID", "imp.editBox.radioUrl": "ラジオURL", "imp.widget.volume": "音量", "imp.widget.playBackControl": "再生コントロール", "imp.widget.loopControl": "ループコントロール", "imp.widget.playProgressControl": "進捗率コントロール", "imp.widget.continuousControl": "連続再生コントロール", "imp.text.playerCount": "%s 人のプレイヤー", "imp.text.musicCount": "%s 個の音楽", "imp.text.invitation": "招待", "imp.text.addPlaylistInfo": "%s 個のプレイリスト、 %s 人のプレイヤー、合計 %s 個の音楽", "imp.text.playlistInfo": "%s 個のプレイリスト、 %s 個の音楽", "imp.text.public": "公開", "imp.text.image": "画像", "imp.text.noImage": "画像無し", "imp.text.dropInfo": "ドラッグアンドドロップで指定できます", "imp.text.imageLoad.empty": "URLを入力してください", "imp.text.imageLoad.error": "読み込みエラー!: %s", "imp.text.imageLoad.notImageUrl": "画像のURLではありません", "imp.text.imageLoad.loadingImage": "画像を読み込み中...", "imp.text.imageLoad.optimizationImage": "画像を最適化中...", "imp.text.imageLoad.notImage": "画像ではありません", "imp.text.imageLoad.uploadImage": "画像をアップロード中...", "imp.text.imageLoad.tooManyImages": "画像が多スギます", "imp.text.imageLoad.fileNotFound": "画像が見つかりません", "imp.text.imageLoad.uploadFailure": "アップロード失敗、もう一度やり直してください: %s", "imp.text.imageLoad.directory": "ディレクトリーです", "imp.text.name": "名前", "imp.text.notEntered": "%s が未入力です", "imp.text.publishingSettings": "公開設定", "imp.text.initialAuthority": "初期権限", "imp.text.invite": "招待", "imp.text.uninvited": "未招待", "imp.text.invited": "招待済み", "imp.text.member": "メンバー", "imp.text.invitePlayerByMCIDOrUUID": "UUIDまたは名前からプレイヤーを招待", "imp.text.playbackLoading": "読み込み中...", "imp.text.musicSource": "音楽元", "imp.text.musicChecking": "確認中...", "imp.text.musicGuessing": "推測中...", "imp.text.searching": "検索中...", "imp.text.loaderTypeInfo.auto": "文字列がどの読み込み方式か当ててみます", "imp.text.enterText.default": "識別子", "imp.text.enterText.auto": "識別可能な文字列", "imp.text.enterText.youtube": "YoutubeのビデオIDまたはURL", "imp.text.enterText.soundcloud": "Sound CloudのURL", "imp.text.enterText.neteasecloudmusic": "Netease Cloud Musicの曲ID", "imp.text.enterText.url": "音楽元URL", "imp.text.loadFailure": "読み込み失敗", "imp.text.loadFailure.auto": "推測失敗", "imp.text.musicAuthor": "著者: %s", "imp.text.relayServer": "中継サーバー", "imp.text.relayServer.response": "応答速度: %sms  処理速度: %sms", "imp.text.relayServer.error": "接続失敗: %s", "imp.text.relayServer.connectingChecking": "接続確認中...", "imp.text.relayServer.uploadInfo": "最大アップロード可能サイズは%sです", "imp.text.relayServer.uploading": "ファイルをアップロードしています...", "imp.text.relayServer.warning": "機密情報などの重要なデータはアップロードしないでください！", "imp.text.relayServer.responsibility": "アップロードにより生じた損害の責任は負いかねます。", "imp.text.relayServer.how": "中継サーバーを経由しDiscordにアップロードします。", "imp.text.uploadDropInfo": "ドラッグアンドドロップでアップロード可能", "imp.text.fileUpload.tooManyFiles": "ファイルが多すぎます", "imp.text.fileUpload.fileNotFound": "ファイルが見つかりません", "imp.text.fileUpload.error": "アップロードエラー！: %s", "imp.text.fileUpload.failure": "%s: %s", "imp.text.fileUpload.sizeOver": "容量が大きすぎます", "imp.text.fileUpload.directory": "ディレクトリです", "imp.text.fileUpload.noURL": "URLが空です", "imp.text.unknownPlayer": "不明", "imp.text.noAntenna": "アンテナがありません", "imp.text.noCassetteTape": "カセットテープをセットしてください", "imp.text.writing": "書き込み中...", "imp.text.noMusicCassetteTape": "音楽が書き込まれていません", "imp.text.musicLoading": "音楽を読み込み中...", "imp.text.streamLoading": "ストリーム音楽を読み込み中...", "imp.text.streamPlaying": "ストリーム音楽を再生中...", "imp.text.playlistLoading": "プレイリストを読み込み中...", "imp.text.enterStream": "ラジオまたはYouTubeライブのURLを入力", "imp.text.authority.owner": "オーナー", "imp.text.authority.admin": "管理者", "imp.text.authority.member": "メンバー", "imp.text.authority.read_only": "読み込み専用", "imp.text.authority.invitation": "招待中", "imp.text.authority.ban": "禁止", "imp.text.authority.none": "無効", "imp.text.importMusicCount": "%s 個の音楽", "imp.text.importing": "インポート中...", "imp.text.importFailure": "インポート失敗", "imp.text.deleteWarning": "「%s」は完全に削除され、二度と元に戻せません！", "imp.text.cantChangeAuthority": "あなたは権限を変更できません", "imp.text.radioChecking": "確認中...", "imp.text.continuous.none": "無効", "imp.text.continuous.order": "順番", "imp.text.continuous.random": "ランダム", "imp.text.manual": "取扱説明書", "imp.text.manual.coverInfo": "はっきりわかんだね！", "imp.fileChooser.files.all": "すべてのファイル", "imp.loaderType.auto": "自動", "imp.loaderType.upload": "アップロード", "imp.loaderType.youtube": "Youtube", "imp.loaderType.http": "Http URL", "imp.loaderType.soundcloud": "Sound Cloud", "imp.loaderType.neteasecloudmusic": "Netease Cloud Music", "imp.fileChooser.title.image": "画像を開く", "imp.fileChooser.title.music": "音楽を開く", "imp.ringer.have": "%s、%sが持つ", "imp.ringer.drop": "ドロップした%s", "soundCategory.iammusicplayer": "IMPの音楽", "imp.button.config": "IMPのコンフィグ", "_comment.co": "Config", "text.autoconfig.iammusicplayer.title": "Iam Music Playerの設定", "text.autoconfig.iammusicplayer.category.client": "クライアント側", "text.autoconfig.iammusicplayer.option.volume": "音量", "text.autoconfig.iammusicplayer.option.maxPlayCont": "最大同時再生数", "text.autoconfig.iammusicplayer.option.spatial": "空間的", "text.autoconfig.iammusicplayer.option.sampleRate": "サンプルレート", "text.autoconfig.iammusicplayer.option.useYoutubeDownloader": "YoutubeDownloaderを利用する", "text.autoconfig.iammusicplayer.option.relayServerURL": "中継サーバーURL", "text.autoconfig.iammusicplayer.option.lavaPlayerNativesURL": "LavaPlayerネイティブライブラリURL", "text.autoconfig.iammusicplayer.option.neteaseCloudMusicApiURL": "NeteaseCloudMusicAPIのURL", "text.autoconfig.iammusicplayer.option.hideDisplaySprite": "ディスプレイスプライトを非表示にする", "text.autoconfig.iammusicplayer.option.hideDecorativeAntenna": "装飾用アンテナを非表示にする", "text.autoconfig.iammusicplayer.category.server": "サーバー側", "text.autoconfig.iammusicplayer.option.maxWaitTime": "最大待機時間", "text.autoconfig.iammusicplayer.option.retryTime": "再試行時間", "text.autoconfig.iammusicplayer.option.dropItemRing": "アイテムドロップ状態で再生するかどうか", "text.autoconfig.iammusicplayer.category.integration": "連携", "text.autoconfig.iammusicplayer.option.patchouliIntegration": "<PERSON><PERSON><PERSON>との連携", "text.autoconfig.iammusicplayer.option.soundPhysicsRemasteredIntegration": "SoundPhysicsRemasteredとの連携", "text.autoconfig.iammusicplayer.category.debug": "デバッグ", "text.autoconfig.iammusicplayer.option.showMusicLines": "音楽のタイムラインを表示", "text.autoconfig.iammusicplayer.option.showSpeakerRange": "スピーカーの範囲を表示", "_comment.cc": "Config Value", "subtitleType.off": "オフ", "subtitleType.vanilla": "バニラ", "subtitleType.overlay": "オーバーレイ", "_comment.c": "Command", "commands.imp.ringer.info": "現在存在する音源は %s 個、再生中は %s 個です (%s)", "commands.imp.ringer.info.all": "現在存在する音源は %s 個、再生中は %s 個です", "commands.imp.ringer.list.all": "存在する音源は以下の通りです", "commands.imp.ringer.list": "%s に存在する音源は以下の通りです", "commands.imp.ringer.list.all.notFound": "音源は見つかりませんでした", "commands.imp.ringer.list.notFound": "%s に存在する音源は見つかりませんでした", "commands.imp.ringer.list.entry.playing": "%s、場所は %s、現在再生中", "commands.imp.ringer.list.entry": "%s、場所は %s", "commands.imp.ringer.list.all.entry.playing": "%s、場所は %s (%s)、現在再生中", "commands.imp.ringer.list.all.entry": "%s、場所は %s (%s)", "_comment.e": "Entity", "entity.minecraft.villager.dj": "DJ", "entity.minecraft.villager.iammusicplayer.dj": "DJ", "entity.minecraft.villager.iammusicplayer:dj": "DJ", "_comment.a": "Advancements", "advancements.iammusicplayer.root.title": "<PERSON>am Music Player", "advancements.iammusicplayer.root.description": "とてもイキスギた音楽プレイヤーMOD", "advancements.iammusicplayer.add_music.title": "音楽の登録者", "advancements.iammusicplayer.add_music.description": "音楽管理装置を使用してプレイリストに音楽を追加する", "advancements.iammusicplayer.write_cassette_tape.title": "書き込み中です...", "advancements.iammusicplayer.write_cassette_tape.description": "カセットデッキを使用してカセットテープに音楽を書き込む", "advancements.iammusicplayer.listen_to_music.title": "DJ! DJ!", "advancements.iammusicplayer.listen_to_music.description": "音楽を聴く", "advancements.iammusicplayer.listen_to_radio.title": "生放送", "advancements.iammusicplayer.listen_to_radio.description": "ラジオストリームを聴く", "advancements.iammusicplayer.listen_to_remote_music.title": "直接聞く", "advancements.iammusicplayer.listen_to_remote_music.description": "パラボラアンテナを使用してプレイリストを直接再生する", "advancements.iammusicplayer.listen_to_kamesuta.title": "かめぱわ～", "advancements.iammusicplayer.listen_to_kamesuta.description": "カメスタアンテナを使用してカメスタの動画を再生する", "_comment.bo": "Book", "book.imp.landing_text": "この本はIamMusicPlayerについて解説しています。$(br2)もしバグや他MODとの競合を発見した場合は$(l:https://github.com/TeamFelnull/IamMusicPlayer/issues)GitHub issues$(/l)にてお知らせください。", "_comment.cr": "CreativeTab", "itemGroup.iammusicplayer.iammusicplayer": "<PERSON>am Music Player", "_comment.other": "Other", "modmenu.descriptionTranslation.iammusicplayer": "複数のプレイヤーと聞ける音楽プレイヤーを追加する\nイキスギ音楽プレイヤーMOD...", "_comment.la": "Last"}